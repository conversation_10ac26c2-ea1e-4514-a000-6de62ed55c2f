import{r,a as gs,b as qe}from"./vendor-280e31ee.js";import{N as ss,u as Oe,a as ps,L as fs,O as ys,b as bs,B as js,R as ws,c as ce}from"./router-208768c5.js";import{C as Ns,X as vs,A as ks,I as Ss,M as Ps,U as Ae,S as We,L as As,a as He,b as Cs,T as je,c as pe,B as ts,d as Ce,e as as,f as Ds,E as Rs,g as $s,h as Es,F as Ge,i as Ie,H as Qe,D as de,j as Ts,k as Ls,P as Ne,R as ae,l as rs,m as De,n as _s,o as Us,p as Fs,q as Me,r as Ve,s as Bs,t as Is,u as ns,K as Ms,v as Os,w as Fe,x as ls,y as Hs,z as zs}from"./icons-9d7a79a3.js";import{R as ve,B as os,C as Re,X as $e,Y as Ee,T as ke,a as is,L as Te,b as Je,c as Ye,P as Ks,d as Vs,e as qs,S as Xe,f as Ws}from"./charts-2d8bc326.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))b(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const m of c.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&b(m)}).observe(document,{childList:!0,subtree:!0});function a(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function b(o){if(o.ep)return;o.ep=!0;const c=a(o);fetch(o.href,c)}})();var cs={exports:{}},ze={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Js=r,Ys=Symbol.for("react.element"),Gs=Symbol.for("react.fragment"),Qs=Object.prototype.hasOwnProperty,Xs=Js.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Zs={key:!0,ref:!0,__self:!0,__source:!0};function ds(s,n,a){var b,o={},c=null,m=null;a!==void 0&&(c=""+a),n.key!==void 0&&(c=""+n.key),n.ref!==void 0&&(m=n.ref);for(b in n)Qs.call(n,b)&&!Zs.hasOwnProperty(b)&&(o[b]=n[b]);if(s&&s.defaultProps)for(b in n=s.defaultProps,n)o[b]===void 0&&(o[b]=n[b]);return{$$typeof:Ys,type:s,key:c,ref:m,props:o,_owner:Xs.current}}ze.Fragment=Gs;ze.jsx=ds;ze.jsxs=ds;cs.exports=ze;var e=cs.exports,ms,Ze=gs;ms=Ze.createRoot,Ze.hydrateRoot;const us=()=>window.location.origin,Be=async(s,n={})=>{const a=localStorage.getItem("adminToken"),b=us(),o={headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}},c={...o,...n,headers:{...o.headers,...n.headers}},m=await fetch(`${b}${s}`,c);if(m.status===401){let w="Authentication failed. Please login again.";try{const $=await m.json();$.message&&$.message.includes("expired")?w="Your session has expired. Please login again.":$.message&&$.message.includes("token")&&(w="Invalid session. Please login again.")}catch{}throw window.dispatchEvent(new CustomEvent("auth-error",{detail:{message:w}})),new Error(w)}return m},xs=r.createContext(),fe=()=>{const s=r.useContext(xs);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},et=({children:s})=>{const[n,a]=r.useState(!1),[b,o]=r.useState(null),[c,m]=r.useState(!0),[w,$]=r.useState(null),N=(v="Session expired. Please login again.")=>{console.warn("Auto-logout triggered:",v),window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),a(!1),o(null),window.toast&&window.toast(v,"warning"),window.location.href="/login"};r.useEffect(()=>{const v=localStorage.getItem("adminToken"),f=localStorage.getItem("adminUser"),i=localStorage.getItem("redirectAfterLogin");v&&f&&(a(!0),o(JSON.parse(f))),i&&$(i),m(!1);const j=R=>{const{message:P}=R.detail;N(P)};return window.addEventListener("auth-error",j),()=>{window.removeEventListener("auth-error",j)}},[]);const S={isAuthenticated:n,user:b,login:async(v,f)=>{try{const i=us();console.log(`[AUTH] ${new Date().toISOString()} - Attempting login to:`,`${i}/api/auth/login`),console.log(`[AUTH] Login attempt for email: ${v?v.substring(0,3)+"***":"undefined"}`);const j=new AbortController,R=setTimeout(()=>j.abort(),3e4),P=await fetch(`${i}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:v,password:f}),signal:j.signal});clearTimeout(R),console.log(`[AUTH] Response status: ${P.status}`),console.log("[AUTH] Response headers:",Object.fromEntries(P.headers.entries()));const B=await P.text();console.log("[AUTH] Response text (first 200 chars):",B.substring(0,200));let E;try{E=JSON.parse(B),console.log("[AUTH] Parsed response data:",{success:E.success,message:E.message})}catch(D){console.error("[AUTH] Failed to parse response as JSON:",D),console.error("[AUTH] Raw response text:",B);let A="Server returned invalid response. Please try again.";return B.includes("Internal Server Error")?A="Server is experiencing issues. Please try again in a few moments.":B.includes("timeout")?A="Request timed out. Please check your connection and try again.":B.includes("Database")?A="Database connection issue. Please try again.":P.status>=500?A="Server error occurred. Please try again later.":P.status===404&&(A="Login service not found. Please contact support."),{success:!1,message:A}}if(P.ok&&E.success){console.log("[AUTH] Login successful, storing user data"),localStorage.setItem("adminToken",E.token),localStorage.setItem("adminUser",JSON.stringify(E.user)),a(!0),o(E.user);const D=localStorage.getItem("redirectAfterLogin");return D?(localStorage.removeItem("redirectAfterLogin"),$(null),{success:!0,redirectTo:D}):{success:!0}}else return console.log(`[AUTH] Login failed: ${E.message||"Unknown error"}`),{success:!1,message:E.message||"Login failed. Please check your credentials."}}catch(i){console.error("[AUTH] Login error:",{name:i.name,message:i.message,stack:i.stack});let j="Login failed. Please try again.";return i.name==="AbortError"?j="Request timed out. Please check your connection and try again.":i.message.includes("fetch")?j="Network error. Please check your connection and try again.":i.message.includes("NetworkError")&&(j="Network connection failed. Please try again."),{success:!1,message:j}}},logout:(v=!1)=>{v&&window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),a(!1),o(null)},autoLogout:N,loading:c,redirectPath:w};return e.jsx(xs.Provider,{value:S,children:s})},st=({message:s,type:n="info",duration:a=3e3,onClose:b})=>{const[o,c]=r.useState(!0);r.useEffect(()=>{const N=setTimeout(()=>{c(!1),setTimeout(b,300)},a);return()=>clearTimeout(N)},[a,b]);const m={success:Ns,error:vs,warning:ks,info:Ss},w={success:"bg-green-50 text-green-800 border-green-200",error:"bg-red-50 text-red-800 border-red-200",warning:"bg-yellow-50 text-yellow-800 border-yellow-200",info:"bg-blue-50 text-blue-800 border-blue-200"},$=m[n];return e.jsx("div",{className:`fixed top-4 right-4 z-50 transition-all duration-300 ${o?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"}`,children:e.jsxs("div",{className:`flex items-center p-4 rounded-lg border shadow-lg ${w[n]}`,children:[e.jsx($,{className:"h-5 w-5 mr-3 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s})]})})},tt=({children:s})=>{const[n,a]=r.useState([]),b=(c,m="info",w=3e3)=>{const $=Date.now();a(N=>[...N,{id:$,message:c,type:m,duration:w}])},o=c=>{a(m=>m.filter(w=>w.id!==c))};return qe.useEffect(()=>{window.toast=b},[]),e.jsxs(e.Fragment,{children:[s,n.map(c=>e.jsx(st,{message:c.message,type:c.type,duration:c.duration,onClose:()=>o(c.id)},c.id))]})},at=({children:s})=>{const{isAuthenticated:n,loading:a}=fe();return a?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):n?s:e.jsx(ss,{to:"/login",replace:!0})},rt=()=>{const[s,n]=r.useState(!1),a=r.useRef(null),{logout:b,user:o}=fe(),c=Oe();r.useEffect(()=>{const N=y=>{a.current&&!a.current.contains(y.target)&&n(!1)};return document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}},[]);const m=()=>{b(),c("/login"),n(!1)},w=N=>{c(N),n(!1)},$=()=>{const N=[{label:"Profile",icon:Ae,onClick:()=>w("/profile")}];return((o==null?void 0:o.role)==="admin"||(o==null?void 0:o.role)==="superadmin")&&N.push({label:"Settings",icon:We,onClick:()=>w("/settings")}),N.push({label:"Logout",icon:As,onClick:m,className:"text-red-600 hover:bg-red-50"}),N};return e.jsxs("div",{className:"relative",ref:a,children:[e.jsx("button",{onClick:()=>n(!s),className:"flex items-center p-2 text-gray-600 hover:bg-[#edf1f7] hover:text-gray-800 rounded-md transition-colors",title:"More options",children:e.jsx(Ps,{className:"h-4 w-4"})}),s&&e.jsx("div",{className:"absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:$().map((N,y)=>{const _=N.icon;return e.jsxs("button",{onClick:N.onClick,className:`w-full flex items-center px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${N.className||"text-gray-700"}`,children:[e.jsx(_,{className:"h-4 w-4 mr-3"}),N.label]},y)})})]})},nt=s=>{let n=0;if(s.length===0)return n.toString();for(let a=0;a<s.length;a++){const b=s.charCodeAt(a);n=(n<<5)-n+b,n=n&n}return Math.abs(n).toString(16)},lt=(s,n=32)=>`https://www.gravatar.com/avatar/${nt(s)}?s=${n}&d=identicon`,ot=()=>{const{user:s}=fe(),n=ps(),[a,b]=r.useState(!1),o=()=>{const c=[{name:"Dashboard",path:"/dashboard",icon:Cs},{name:"Plugin Rank",path:"/plugin-rank",icon:je},{name:"Keyword Analysis",path:"/keyword-analysis",icon:pe},{name:"Plugin Data Analysis",path:"/analytics",icon:ts}];return((s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="superadmin")&&c.push({name:"Team Members",path:"/users",icon:Ce}),c};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>b(!1)}),e.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${a?"translate-x-0":"-translate-x-full"}`,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[e.jsx("img",{src:"/wpdev.png",className:"",alt:"WPDeveloper Logo"}),e.jsx("button",{onClick:()=>b(!1),className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:e.jsx(He,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"mt-6",children:o().map(c=>{const m=c.icon,w=n.pathname===c.path;return e.jsxs(fs,{to:c.path,onClick:()=>b(!1),className:`flex items-center px-6 py-3 text-sm font-medium transition-colors ${w?"bg-blue-50 text-blue-600 border-r-2 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[e.jsx(m,{className:"h-5 w-5 mr-3"}),c.name]},c.path)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full overflow-hidden",children:e.jsx("img",{src:(s==null?void 0:s.profileImage)||lt((s==null?void 0:s.email)||""),alt:"Profile",className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:(s==null?void 0:s.name)||"Admin User"}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:(s==null?void 0:s.role)||"member"})]})]}),e.jsx(rt,{})]})})]}),e.jsx("div",{className:"lg:pl-64",children:e.jsx("main",{className:"p-6",children:e.jsx(ys,{})})})]})},it=()=>{const[s,n]=r.useState(""),[a,b]=r.useState(""),[o,c]=r.useState(!1),[m,w]=r.useState(!1),[$,N]=r.useState(""),{login:y}=fe(),_=Oe(),S=async v=>{v.preventDefault(),N(""),w(!0);const f=await y(s,a);if(f.success){const i=f.redirectTo||"/dashboard";_(i)}else N(f.message||"Login failed");w(!1)};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4",children:e.jsx("img",{src:"/wpdev_logo.jpeg",className:"h-16 w-16 rounded-full border"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your admin account"})]}),e.jsxs("form",{onSubmit:S,className:"space-y-6",children:[$&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:$}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(as,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"email",type:"email",value:s,onChange:v=>n(v.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ds,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"password",type:o?"text":"password",value:a,onChange:v=>b(v.target.value),className:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0}),e.jsx("button",{type:"button",onClick:()=>c(!o),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:o?e.jsx(Rs,{className:"h-5 w-5"}):e.jsx($s,{className:"h-5 w-5"})})]})]}),e.jsx("button",{type:"submit",disabled:m,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:m?"Signing in...":"Sign In"})]})]})})})},ge=({isOpen:s,onClose:n,title:a,children:b,maxWidth:o="max-w-xl",fixedHeight:c=!1})=>(r.useEffect(()=>{const m=w=>{w.key==="Escape"&&n()};return s&&(document.addEventListener("keydown",m),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",m),document.body.style.overflow="unset"}},[s,n]),s?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:n}),e.jsxs("div",{className:`relative bg-white rounded-lg shadow-xl ${o} w-full mx-4 transform transition-all ${c?"h-[90vh] flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a}),e.jsx("button",{onClick:n,className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(He,{className:"h-5 w-5 text-gray-500"})})]}),e.jsx("div",{className:`p-6 ${c?"flex-1 overflow-y-auto":""}`,children:b})]})]})}):null),ct=({isOpen:s,onClose:n,plugin:a})=>{const b=Oe(),o=r.useRef(null),[c,m]=r.useState([]),[w,$]=r.useState([]),[N,y]=r.useState(0),[_,S]=r.useState(0),[v,f]=r.useState([]),[i,j]=r.useState({}),[R,P]=r.useState(null),[B,E]=r.useState({}),[D,A]=r.useState(""),[g,p]=r.useState(""),[k,C]=r.useState(!1),[L,T]=r.useState(!0),[O,I]=r.useState(!0),[V,G]=r.useState(!0),[J,ee]=r.useState(!0),[re,le]=r.useState(0),ne=(t,h)=>{le(h)},ye=async()=>{if(a)try{T(!0);const t=localStorage.getItem("adminToken"),d=await fetch(`https://pluginsight.vercel.app/api/analytics/download-data/${a.slug}?days=15`,{headers:{Authorization:`Bearer ${t}`}});if(!d.ok)throw new Error("Failed to fetch download data");const u=await d.json();if(u.success&&u.downloadData){const M=u.downloadData.map(F=>({date:new Date(F.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:F.downloads,fullDate:F.date}));m(M)}else m([])}catch(t){console.error("Error fetching download data:",t),m([])}finally{T(!1)}},me=async()=>{if(a)try{I(!0);const t=localStorage.getItem("adminToken"),d=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-info/${a.slug}`,{headers:{Authorization:`Bearer ${t}`}});if(!d.ok)throw new Error("Failed to fetch plugin information");const u=await d.json();u.success&&u.ratings?($(u.ratings),y(u.totalRatings),S(u.averageRating||0)):($([]),y(0),S(0))}catch(t){console.error("Error fetching ratings data:",t),$([]),y(0),S(0)}finally{I(!1)}},be=async()=>{if(a)try{G(!0);const t=localStorage.getItem("adminToken"),d=await fetch(`https://pluginsight.vercel.app/api/analytics/rank-history/${a.slug}?days=15`,{headers:{Authorization:`Bearer ${t}`}});if(!d.ok)throw new Error("Failed to fetch rank history");const u=await d.json();if(u.success&&u.rankHistory){const M=u.rankHistory.map(F=>({date:F.date,rank:F.rank,fetchedAt:F.fetchedAt}));f(M)}else f([])}catch(t){console.error("Error fetching rank history:",t),f([])}finally{G(!1)}},oe=async()=>{if(a)try{ee(!0);const t=localStorage.getItem("adminToken"),d=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-versions/${a.slug}`,{headers:{Authorization:`Bearer ${t}`}});if(!d.ok)throw new Error("Failed to fetch plugin versions");const u=await d.json();u.success?(j(u.versions||{}),P(u.currentVersion),E(u.oldVersions||{})):(j({}),P(null),E({}))}catch(t){console.error("Error fetching versions data:",t),j({}),P(null),E({})}finally{ee(!1)}};r.useEffect(()=>{s&&a&&(ye(),me(),be(),oe())},[s,a]),r.useEffect(()=>{const t=h=>{o.current&&!o.current.contains(h.target)&&(C(!1),p(""))};if(k)return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[k]);const se=t=>{var h;return t?((h=t.split(/[-–:]|&#8211;/)[0])==null?void 0:h.trim())||t:""},te=()=>!B||Object.keys(B).length===0?[]:Object.keys(B).filter(t=>t.toLowerCase()==="trunk"||t===R?!1:g.trim()?t.toLowerCase().includes(g.toLowerCase()):!0).sort((t,h)=>{const d=F=>F.split(".").map(q=>parseInt(q)||0),u=d(t),M=d(h);for(let F=0;F<Math.max(u.length,M.length);F++){const q=(M[F]||0)-(u[F]||0);if(q!==0)return q}return 0}),ue=t=>{A(t),C(!1),p("")},x=()=>{C(!k),k||p("")},U=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6"],Q=t=>{const h=Math.PI/180,{cx:d,cy:u,midAngle:M,innerRadius:F,outerRadius:q,startAngle:Se,endAngle:we,fill:ie,payload:H,percent:Le,value:Ke}=t,_e=Math.sin(-h*M),xe=Math.cos(-h*M),l=d+(q+10)*xe,z=u+(q+10)*_e,W=d+(q+30)*xe,Y=u+(q+30)*_e,K=W+(xe>=0?1:-1)*22,X=Y,Z=xe>=0?"start":"end";return e.jsxs("g",{children:[e.jsxs("text",{x:d,y:u,dy:8,textAnchor:"middle",fill:ie,children:[H.stars,"★"]}),e.jsx(Xe,{cx:d,cy:u,innerRadius:F,outerRadius:q,startAngle:Se,endAngle:we,fill:ie}),e.jsx(Xe,{cx:d,cy:u,startAngle:Se,endAngle:we,innerRadius:q+6,outerRadius:q+10,fill:ie}),e.jsx("path",{d:`M${l},${z}L${W},${Y}L${K},${X}`,stroke:ie,fill:"none"}),e.jsx("circle",{cx:K,cy:X,r:2,fill:ie,stroke:"none"}),e.jsx("text",{x:K+(xe>=0?1:-1)*12,y:X,textAnchor:Z,fill:"#333",children:`${Ke} ratings`}),e.jsx("text",{x:K+(xe>=0?1:-1)*12,y:X,dy:18,textAnchor:Z,fill:"#999",children:`(${(Le*100).toFixed(1)}%)`})]})};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Es,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[se((a==null?void 0:a.displayName)||(a==null?void 0:a.name))," ","Analytics"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download trends and rating analysis"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>b(`/plugin-details/${a==null?void 0:a.slug}`),className:"flex items-center space-x-2 px-3 py-2 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors",title:"View Plugin Details",children:[e.jsx(Ge,{className:"h-4 w-4"}),e.jsx("span",{children:"Plugin Details"})]}),e.jsx("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors p-2",children:e.jsx(He,{className:"h-6 w-6"})})]})]}),e.jsxs("div",{className:"flex-1 p-6 space-y-4 overflow-y-auto",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(je,{className:"h-5 w-5 mr-2 text-blue-600"}),"Download Trends (Last 15 Days)"]}),L?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):c.length>0?e.jsx(ve,{width:"100%",height:300,children:e.jsxs(os,{data:c,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date"}),e.jsx(Ee,{}),e.jsx(ke,{formatter:t=>[t.toLocaleString(),"Downloads"],labelFormatter:t=>`Date: ${t}`}),e.jsx(is,{dataKey:"downloads",fill:"#3B82F6",children:e.jsx(Te,{dataKey:"downloads",position:"top",fontSize:10,formatter:t=>t.toLocaleString()})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No download data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(je,{className:"h-5 w-5 mr-2 text-purple-600"}),"15-Day Rank Change"]}),V?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})}):v.length>0?e.jsx(ve,{width:"100%",height:300,children:e.jsxs(Je,{data:v,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date"}),e.jsx(Ee,{domain:["dataMin - 10","dataMax + 10"],reversed:!0,tickFormatter:t=>`#${t}`}),e.jsx(ke,{formatter:t=>[`#${t}`,"Rank"],labelFormatter:t=>`Date: ${t}`}),e.jsx(Ye,{type:"monotone",dataKey:"rank",stroke:v.length>1&&v[v.length-1].rank<v[0].rank?"#10B981":"#EF4444",strokeWidth:2,dot:{fill:v.length>1&&v[v.length-1].rank<v[0].rank?"#10B981":"#EF4444",strokeWidth:2,r:4},activeDot:{r:6,strokeWidth:2},children:e.jsx(Te,{dataKey:"rank",position:"top",formatter:t=>`#${t}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rank history data available"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Ie,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Rating Distribution"]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-lg font-bold text-gray-900 flex items-center",children:[_?(_/20).toFixed(1):"N/A"," ⭐"]})})]}),O?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"})}):w.length>0?e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 flex-1",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(ve,{width:200,height:200,children:e.jsxs(Ks,{children:[e.jsx(Vs,{activeIndex:re,activeShape:Q,data:[...w].sort((t,h)=>h.stars-t.stars),cx:"50%",cy:"50%",innerRadius:40,outerRadius:60,fill:"#8884d8",dataKey:"value",onMouseEnter:ne,children:w.map((t,h)=>e.jsx(qs,{fill:U[h%U.length]},`cell-${h}`))}),e.jsx(ke,{formatter:(t,h,d)=>[`${t} ratings`,`${d.payload.stars} Star${d.payload.stars!==1?"s":""}`]})]})})})}),e.jsxs("div",{className:"bg-white rounded-lg p-4 space-y-2 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Breakdown"}),e.jsx("div",{className:"space-y-2",children:[...w].sort((t,h)=>h.stars-t.stars).map((t,h)=>{const d=w.length>0?t.value/w.reduce((u,M)=>u+M.value,0)*100:0;return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex items-center space-x-1 w-12",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700",children:t.stars}),e.jsx(Ie,{className:"h-3 w-3 text-yellow-400 fill-current"})]}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500",style:{width:`${d}%`,backgroundColor:U[h%U.length]}})}),e.jsx("div",{className:"text-xs font-medium text-gray-900 w-8 text-right",children:t.value})]},t.stars)})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-2",children:[e.jsx("div",{className:"text-xs font-bold text-gray-500",children:"Total"}),e.jsx("div",{className:"text-xs font-medium text-gray-900",children:N})]})]})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rating data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(Ge,{className:"h-5 w-5 mr-2 text-blue-600"}),"Plugin Downloads"]}),J?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.keys(i).length>0?e.jsxs("div",{className:"space-y-4",children:[R&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(Qe,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:["Version ",R]}),e.jsx("span",{className:"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full",children:"Current"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Latest stable release"})]})]}),e.jsx("a",{href:i[R],target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center transition-colors",title:"Download Current Version",children:e.jsx(de,{className:"h-4 w-4"})})]})}),Object.keys(B).length>0&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(Qe,{className:"h-5 w-5 text-gray-600"})}),e.jsx("span",{className:"font-semibold text-gray-900",children:"Previous Versions"}),e.jsx("span",{className:"bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full",children:"Archive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",ref:o,children:[e.jsxs("button",{type:"button",onClick:x,className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between",children:[e.jsx("span",{className:D?"text-gray-900":"text-gray-500",children:D||"Select a version"}),k?e.jsx(Ts,{className:"h-4 w-4 text-gray-400"}):e.jsx(Ls,{className:"h-4 w-4 text-gray-400"})]}),k&&e.jsxs("div",{className:"absolute z-10 w-full bottom-full mb-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:[e.jsx("div",{className:"p-2 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search versions...",value:g,onChange:t=>p(t.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent",autoFocus:!0})]})}),e.jsx("div",{className:"max-h-48 overflow-y-auto",children:te().length>0?te().map(t=>e.jsx("button",{type:"button",onClick:()=>ue(t),className:"w-full px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors",children:t},t)):e.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:g.trim()?"No versions found":"No versions available"})})]})]}),e.jsx("div",{children:e.jsx("a",{href:D?B[D]:"#",target:D?"_blank":"_self",rel:"noopener noreferrer",className:`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ml-3 ${D?"bg-gray-600 hover:bg-gray-700 text-white cursor-pointer":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:D?"Download Selected Version":"Select a version first",onClick:D?void 0:t=>t.preventDefault(),children:e.jsx(de,{className:"h-4 w-4"})})})]})]})})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No version data available"})]})]})]})]})}):null},dt=s=>{if(!s||typeof s!="string")return!1;const n=s.split(".");if(n.length!==3)return!1;try{return n.forEach(a=>{if(a.length===0)throw new Error("Empty JWT part");atob(a.replace(/-/g,"+").replace(/_/g,"/"))}),!0}catch{return!1}},Pe=()=>{const s=localStorage.getItem("adminToken");return s?dt(s)?s:(console.warn("Invalid JWT token found, clearing localStorage"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),null):null},es=(s,n)=>{var a;((n==null?void 0:n.status)===401||(a=s==null?void 0:s.message)!=null&&a.includes("token"))&&(console.warn("Authentication error detected, clearing tokens"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.pathname!=="/login"&&(window.location.href="/login"))},mt=({plugin:s,onRemove:n,onRefresh:a,canAddPlugins:b})=>{var y,_;const[o,c]=r.useState(!1),[m,w]=r.useState(!1),$=S=>{if(!S)return{formatted:"N/A",daysDiff:"N/A"};try{const v=S.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!v)return{formatted:"N/A",daysDiff:"N/A"};const[,f,i,j]=v,R=`${j}-${i}-${f}`,P=new Date(`${f}-${i}-${j}`),B=new Date;if(isNaN(P.getTime()))return{formatted:"N/A",daysDiff:"N/A"};const E=B-P,D=Math.floor(E/(1e3*60*60*24));return{formatted:R,daysDiff:D}}catch{return{formatted:"N/A",daysDiff:"N/A"}}},N=async()=>{if(a){w(!0);try{await a(s.slug)}finally{w(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 flex-1 overflow-hidden",children:[e.jsxs("div",{className:"w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden",children:[s.icons&&(s.icons["2x"]||s.icons["1x"])?e.jsx("img",{src:s.icons["2x"]||s.icons["1x"],alt:`${s.displayName} icon`,className:"w-full h-full object-cover rounded-lg",onError:S=>{S.target.style.display="none",S.target.nextSibling.style.display="flex"}}):null,e.jsx(rs,{className:`h-6 w-6 text-black ${s.icons&&(s.icons["2x"]||s.icons["1x"])?"hidden":""}`})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-semibold text-gray-900 truncate text-lg",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-500 font-mono whitespace-nowrap",children:s.slug})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:N,disabled:m,className:"text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50",title:"Refresh plugin data",children:e.jsx(ae,{className:`h-4 w-4 ${m?"animate-spin":""}`})}),b&&e.jsx("button",{onClick:()=>n(s.slug),className:"text-gray-400 hover:text-red-500 transition-colors p-1",title:"Remove plugin",children:e.jsx(De,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-x-4",children:[e.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800",children:["v",s.version||"N/A"]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.currentRank||"N/A"]}),((y=s.rankHistory)==null?void 0:y.rankChange)!==null&&((_=s.rankHistory)==null?void 0:_.rankChange)!==void 0&&e.jsxs("span",{className:`text-xs ${s.rankHistory.rankChange>0?"text-green-600":s.rankHistory.rankChange<0?"text-red-600":"text-gray-600"}`,children:[s.rankHistory.rankChange>0?"↑":s.rankHistory.rankChange<0?"↓":"→",Math.abs(s.rankHistory.rankChange)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Released"}),e.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded ${(()=>{const S=s.lastReleaseDate||s.lastFetched,v=$(S);return v.daysDiff==="N/A"||v.daysDiff<=20?"bg-gray-100 text-gray-700":"bg-yellow-50 text-yellow-700"})()}`,children:(()=>{const S=s.lastReleaseDate||s.lastFetched,v=$(S);return e.jsxs(e.Fragment,{children:[v.formatted,v.daysDiff!=="N/A"&&e.jsxs("span",{className:"text-xs ml-1",children:["(",v.daysDiff," days)"]})]})})()})]})]}),e.jsxs("div",{className:"mt-4 border border-gray-200 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 px-3 py-2 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Download Trends"}),s.downloadTrend&&e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${s.downloadTrend.isPositive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[s.downloadTrend.isPositive?"↑":"↓"," ",s.downloadTrend.changePercent,"%"]})]})}),s.downloadTrend?e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Yesterday"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right",children:s.downloadTrend.yesterdayDownloads.toLocaleString()})]}),e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Day Before"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right",children:s.downloadTrend.dayBeforeDownloads.toLocaleString()})]})]}),e.jsx("tfoot",{className:"bg-gray-100 border-t border-gray-200",children:e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 text-sm font-semibold text-gray-900",children:"Changes"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:e.jsxs("span",{className:`text-sm font-bold ${s.downloadTrend.change>=0?"text-green-600":"text-red-600"}`,children:[s.downloadTrend.change>=0?"+":"",s.downloadTrend.change.toLocaleString()]})})]})})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("span",{className:"text-xs text-gray-500",children:"No download trend data available"})})]}),e.jsx("div",{className:"pt-4 border-t border-gray-100",children:e.jsxs("button",{onClick:()=>c(!0),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2",children:[e.jsx(_s,{className:"h-4 w-4"}),e.jsx("span",{children:"View Chart & Analytics"})]})})]}),e.jsx(ct,{isOpen:o,onClose:()=>c(!1),plugin:s})]})},ut=()=>{var ue,x,U,Q;const{user:s,autoLogout:n}=fe(),[a,b]=r.useState(!1),[o,c]=r.useState(""),[m,w]=r.useState(!1),[$,N]=r.useState(!1),[y,_]=r.useState(null),[S,v]=r.useState([]),[f,i]=r.useState(null),[j,R]=r.useState(!1),[P,B]=r.useState(!1),[E,D]=r.useState(null),[A,g]=r.useState(!1),[p,k]=r.useState(!1),[C,L]=r.useState(null),[T,O]=r.useState(""),I=s&&["admin","superadmin"].includes(s.role),V=async()=>{try{const t=Pe();if(!t){console.warn("No valid authentication token found for database check"),n("No valid authentication token found. Please login again.");return}const d=await fetch("https://pluginsight.vercel.app/api/plugins/check-database",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(d.ok){const u=await d.json();u.success&&N(u.hasPlugins)}else if(d.status===401){console.warn("Authentication failed during database check");let u="Your session has expired. Please login again.";try{const M=await d.json();M.message&&(u=M.message)}catch{}n(u)}else es(null,d)}catch(t){if(console.error("Error checking database status:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during database check:",t.message),n(t.message);return}es(t)}},G=async()=>{try{w(!0),_({current:0,total:0,page:0,totalPages:0,successCount:0,errorCount:0,percentComplete:0,estimatedTimeRemaining:null,averageTimePerPage:null,pluginsPerSecond:0,message:"Starting full plugin fetch (all 55,540+ plugins)..."});const t=Pe();if(!t){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const d=await fetch("https://pluginsight.vercel.app/api/plugins/fetch-all",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!d.ok){if(d.status===401){console.warn("Authentication failed during plugin fetch");let F="Your session has expired. Please login again.";try{const q=await d.json();q.message&&(F=q.message)}catch{}n(F);return}throw new Error(`HTTP error! status: ${d.status}`)}const u=d.body.getReader(),M=new TextDecoder;for(;;){const{done:F,value:q}=await u.read();if(F)break;const we=M.decode(q).split(`
`).filter(ie=>ie.trim());for(const ie of we)try{const H=JSON.parse(ie);if(H.type==="progress")_({current:H.current||0,total:H.total||0,page:H.page||0,totalPages:H.totalPages||0,successCount:H.successCount||0,errorCount:H.errorCount||0,percentComplete:H.percentComplete||0,estimatedTimeRemaining:H.estimatedTimeRemaining,averageTimePerPage:H.averageTimePerPage,pluginsPerSecond:H.pluginsPerSecond||0,message:H.message||"Processing..."});else if(H.type==="complete")_({current:H.summary.totalProcessedPlugins,total:H.summary.totalPlugins,page:H.summary.totalPages,totalPages:H.summary.totalPages,successCount:H.summary.successfulPages,errorCount:H.summary.failedPages,percentComplete:100,averageTimePerPage:H.summary.averageTimePerPage,pluginsPerSecond:H.summary.averagePluginsPerSecond,successRate:H.summary.successRate,totalDuration:H.summary.totalDuration,message:`✅ Fetch completed! ${H.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(H.summary.totalDuration/1e3/60)} minutes`}),H.errors&&H.errors.length>0&&console.warn("Some errors occurred during fetch:",H.errors),window.toast(`Successfully fetched ${H.summary.totalProcessedPlugins.toLocaleString()} plugins!`,"success");else if(H.type==="error")throw new Error(H.message||"Fetch failed")}catch(H){console.warn("Failed to parse streaming data:",H)}}}catch(t){if(console.error("Fetch all plugins error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin fetch:",t.message),n(t.message);return}_({current:0,total:0,message:`❌ Full fetch failed: ${t.message}`,error:!0}),window.toast(`Fetch failed: ${t.message}`,"error")}finally{w(!1),setTimeout(()=>_(null),1e4),V()}},J=async()=>{try{const t=await Be("/api/plugins/added");if(!t.ok){let d=`HTTP error! status: ${t.status}`;try{const u=await t.json();u.message&&(d=u.message)}catch{}throw new Error(d)}const h=await t.json();if(h.success){const d=h.addedPlugins.map(u=>{var M;return{slug:u.pluginSlug,name:u.pluginName,displayName:u.displayName,currentRank:u.currentRank,rankGrowth:((M=u.rankHistory)==null?void 0:M.rankChange)||0,lastFetched:u.lastUpdated,short_description:u.short_description,version:u.version,lastReleaseDate:u.lastReleaseDate,icons:u.icons||{},rating:u.rating,numRatings:u.numRatings||0,currentVersion:u.currentVersion,previousVersions:u.previousVersions||[],rankHistory:u.rankHistory,downloadTrend:u.downloadTrend,downloadDataHistory:u.downloadDataHistory||[],reviewStats:u.reviewStats,versionInfo:u.versionInfo,pluginInformation:u.pluginInformation}});v(d)}else console.warn("Failed to load added plugins:",h.message)}catch(t){console.error("Error loading added plugins:",t),t.name==="TypeError"&&t.message.includes("Failed to fetch")?(console.warn("Unable to connect to server - backend may be down"),v([])):(console.warn("Failed to load added plugins:",t.message),v([]))}},ee=t=>{try{localStorage.setItem("pluginData",JSON.stringify(t))}catch(h){console.error("Error storing plugin data in localStorage:",h)}},re=()=>{try{const t=localStorage.getItem("pluginData");return t?JSON.parse(t):null}catch(t){return console.error("Error retrieving plugin data from localStorage:",t),null}},le=()=>{localStorage.removeItem("pluginData")},ne=async()=>{if(!o.trim()){window.toast("Please enter a plugin slug","warning");return}try{R(!0);const t=`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${o.trim()}&request[fields][icons]=true`,h=await fetch(t);if(!h.ok)throw new Error(`WordPress API error: ${h.status}`);const d=await h.json();if(d.error){window.toast(`Plugin not found: ${d.error}`,"error"),i(null);return}const u={slug:d.slug,name:d.name,version:d.version,author:d.author,rating:d.rating,active_installs:d.active_installs,num_ratings:d.num_ratings,downloaded:d.downloaded,last_updated:d.last_updated,short_description:d.short_description,homepage:d.homepage,requires:d.requires,tested:d.tested,requires_php:d.requires_php,icons:d.icons?{"1x":d.icons["1x"],"2x":d.icons["2x"]}:{},tags:d.tags?Object.keys(d.tags).slice(0,10):[]};ee(u),i({slug:d.slug,name:d.name,version:d.version,author:d.author,rating:d.rating,active_installs:d.active_installs,num_ratings:d.num_ratings,downloaded:d.downloaded,last_updated:d.last_updated,homepage:d.homepage,requires:d.requires,tested:d.tested,requires_php:d.requires_php}),window.toast("Plugin data fetched successfully from WordPress API","success")}catch(t){console.error("Error fetching plugin data:",t),window.toast("Failed to fetch plugin data from WordPress API","error"),i(null)}finally{R(!1)}};r.useEffect(()=>{J(),V()},[]);const ye=async()=>{const t=re();if(!t){window.toast("Please fetch plugin data first by clicking the Fetch button","warning");return}if(!o.trim()){window.toast("Please enter a plugin slug","warning");return}g(!0);try{const h=Pe();if(!h){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const u=await fetch("https://pluginsight.vercel.app/api/plugins/added-with-data",{method:"POST",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"},body:JSON.stringify({slug:o.trim(),pluginData:t})});if(!u.ok){if(u.status===401){console.warn("Authentication failed during plugin addition");let F="Your session has expired. Please login again.";try{const q=await u.json();q.message&&(F=q.message)}catch{}n(F);return}else if(u.status===413){console.warn("Payload too large error during plugin addition");try{const F=await u.json();window.toast(F.message||"Plugin data is too large. Please try again or contact support.","error")}catch{window.toast("Plugin data is too large. Please try again or contact support.","error")}return}}const M=await u.json();M.success?(window.toast(M.message,"success"),le(),c(""),b(!1),i(null),await J()):window.toast(M.message||"Failed to add plugin","error")}catch(h){if(console.error("Add plugin error:",h),h.message&&(h.message.includes("expired")||h.message.includes("token")||h.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin addition:",h.message),n(h.message);return}window.toast("Failed to add plugin. Please try again.","error")}finally{g(!1)}},me=t=>{const h=S.find(d=>d.slug===t)||addedPluginsListData.find(d=>d.slug===t);D(h),B(!0)},be=async()=>{if(E)try{const t=Pe();if(!t){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const d=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${E.slug}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!d.ok&&d.status===401){console.warn("Authentication failed during plugin removal");let M="Your session has expired. Please login again.";try{const F=await d.json();F.message&&(M=F.message)}catch{}n(M);return}const u=await d.json();u.success?(window.toast("Plugin removed successfully","success"),await J()):window.toast(u.message||"Failed to remove plugin","error")}catch(t){if(console.error("Remove plugin error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin removal:",t.message),n(t.message);return}window.toast("Failed to remove plugin","error")}finally{B(!1),D(null)}},oe=async t=>{try{const h=Pe();if(!h){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const u=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${t}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}});if(!u.ok&&u.status===401){console.warn("Authentication failed during plugin refresh");let F="Your session has expired. Please login again.";try{const q=await u.json();q.message&&(F=q.message)}catch{}n(F);return}const M=await u.json();M.success?(window.toast(M.message,"success"),await J()):window.toast(M.message||"Failed to refresh plugin","error")}catch(h){if(console.error("Refresh plugin error:",h),h.message&&(h.message.includes("expired")||h.message.includes("token")||h.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin refresh:",h.message),n(h.message);return}window.toast("Failed to refresh plugin","error")}},se=async()=>{try{k(!0),L({current:0,total:0,currentPlugin:null});const t=await Be("/api/plugins/added");if(!t.ok)throw new Error(`Failed to fetch plugins: ${t.status}`);const h=await t.json();if(!h.success||!h.addedPlugins||h.addedPlugins.length===0){window.toast("No plugins found to refresh","warning");return}const d=h.addedPlugins.map(u=>u.slug);L({current:0,total:d.length,currentPlugin:null}),window.toast(`Starting refresh of ${d.length} plugins...`,"info");for(const u of d){const M=d.indexOf(u)+1;L({current:M,total:d.length,currentPlugin:u});try{const q=await(await Be(`/api/plugins/added/${u}/refresh`,{method:"POST"})).json();q.success?console.log(`Successfully refreshed plugin: ${u}`):console.warn(`Failed to refresh plugin ${u}: ${q.message}`)}catch(F){console.error(`Error refreshing plugin ${u}:`,F)}}window.toast("All plugins refreshed successfully!","success"),await J()}catch(t){if(console.error("Refresh all plugins error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during refresh all:",t.message),n(t.message);return}window.toast("Failed to refresh all plugins","error")}finally{k(!1),L(null)}},te=S.filter(t=>{var d,u;if(!T.trim())return!0;const h=T.toLowerCase();return((d=t.name)==null?void 0:d.toLowerCase().includes(h))||((u=t.slug)==null?void 0:u.toLowerCase().includes(h))}).sort((t,h)=>{const d=t.name||t.slug||"",u=h.name||h.slug||"";return d.toLowerCase().localeCompare(u.toLowerCase())});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-900",children:"Welcome to Admin Dashboard"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your plugins, analyze keywords, and track performance all in one place."})]}),e.jsxs("div",{className:"flex gap-4",children:[I&&e.jsx("button",{onClick:G,disabled:m,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${m?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white`,title:"Fetch all 55,540+ plugins from WordPress repository",children:m?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Fetching All..."]}):e.jsxs(e.Fragment,{children:[e.jsx(de,{className:"h-4 w-4 mr-2"}),$?"Refetch":"Fetch"]})}),I&&e.jsxs("button",{onClick:()=>b(!0),disabled:m,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${m?"bg-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700"} text-white`,children:[e.jsx(Ne,{className:"h-4 w-4 mr-2"}),"Add Plugin"]})]})]}),y&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg border ${y.error?"bg-red-50 border-red-200":"bg-blue-50 border-blue-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:`text-sm font-medium ${y.error?"text-red-900":"text-blue-900"}`,children:y.message}),y.total>0&&e.jsxs("span",{className:`text-sm ${y.error?"text-red-700":"text-blue-700"}`,children:[(ue=y.current)==null?void 0:ue.toLocaleString(),"/",(x=y.total)==null?void 0:x.toLocaleString()]})]}),y.page&&y.totalPages&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["Page: ",y.page,"/",y.totalPages]}),e.jsxs("div",{children:["Progress: ",y.percentComplete||0,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["✅ ",(U=y.successCount)==null?void 0:U.toLocaleString()," success"]}),y.errorCount>0&&e.jsxs("div",{className:"text-red-600",children:["❌ ",(Q=y.errorCount)==null?void 0:Q.toLocaleString()," errors"]})]})]}),(y.pluginsPerSecond>0||y.estimatedTimeRemaining)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600",children:[y.pluginsPerSecond>0&&e.jsxs("div",{children:["Speed: ",y.pluginsPerSecond," plugins/sec"]}),y.estimatedTimeRemaining&&e.jsxs("div",{children:["ETA:"," ",Math.round(y.estimatedTimeRemaining/1e3/60)," ","min"]}),y.averageTimePerPage&&e.jsxs("div",{children:["Avg: ",Math.round(y.averageTimePerPage/1e3),"s/page"]}),y.successRate&&e.jsxs("div",{children:["Success Rate: ",y.successRate,"%"]})]}),y.totalDuration&&e.jsx("div",{className:"mb-3 text-xs text-green-700 bg-green-50 p-2 rounded",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:["Duration:"," ",Math.round(y.totalDuration/1e3/60)," ","minutes"]}),e.jsxs("div",{children:["Avg Speed: ",y.pluginsPerSecond," plugins/sec"]}),e.jsxs("div",{children:["Success Rate: ",y.successRate,"%"]}),e.jsxs("div",{children:["Pages: ",y.successCount,"/",y.totalPages]})]})}),y.total>0&&!y.error&&e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-3",children:e.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center",style:{width:`${Math.max(2,y.percentComplete||y.current/y.total*100)}%`},children:e.jsxs("span",{className:"text-xs text-white font-medium",children:[y.percentComplete||Math.round(y.current/y.total*100),"%"]})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Added Plugins"}),e.jsx("div",{className:"text-sm text-gray-500",children:S.length>0?`Showing ${te.length} of ${S.length} plugins`:"No plugins added yet"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[S.length>0&&e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search plugins...",value:T,onChange:t=>O(t.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"})]}),I&&S.length>0&&e.jsx("button",{onClick:se,disabled:p,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${p?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white text-sm`,title:"Refresh all plugins sequentially",children:p?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Refreshing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Refresh All"]})})]})]}),C&&e.jsxs("div",{className:"mb-6 p-4 rounded-lg border bg-blue-50 border-blue-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Refreshing plugins..."}),e.jsxs("span",{className:"text-sm text-blue-700",children:[C.current,"/",C.total]})]}),C.currentPlugin&&e.jsxs("div",{className:"text-sm text-blue-700 mb-2",children:["Currently refreshing:"," ",e.jsx("span",{className:"font-mono",children:C.currentPlugin})]}),e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.max(2,C.current/C.total*100)}%`}})}),e.jsx("div",{className:"text-xs text-blue-600 mt-2",children:"Note: Each plugin refresh has a 60-second delay to avoid overwhelming the backend."})]}),S.length>0?te.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:te.map(t=>e.jsx(mt,{plugin:t,onRemove:me,onRefresh:oe,canAddPlugins:I},t.slug))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(pe,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins found"}),e.jsxs("p",{className:"text-gray-600",children:['No plugins match your search query "',T,'". Try adjusting your search terms.']})]}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(rs,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins added yet"}),e.jsx("p",{className:"text-gray-600",children:'Start tracking your WordPress plugins by adding them to your dashboard using the "Add Plugin" button above.'})]})]}),e.jsx(ge,{isOpen:a,onClose:()=>{A||(b(!1),i(null),c(""),le())},title:"Add New Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pluginSlug",className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"pluginSlug",type:"text",value:o,onChange:t=>c(t.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., my-awesome-plugin"}),e.jsx("button",{onClick:ne,disabled:j||!o.trim(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:j?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Fetching..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(de,{className:"h-4 w-4"}),e.jsx("span",{children:"Fetch"})]})})]})]}),f&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-green-900",children:"Plugin Information (Fetched from WordPress API)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Version:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.version||"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Author:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:(()=>{const t=f.author;if(!t)return"N/A";const h=t.match(/<a[^>]*>(.*?)<\/a>/);return h?h[1]:t})()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Rating:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.rating?`${f.rating}/100`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Active Installs:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.active_installs?f.active_installs.toLocaleString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Last Updated:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:f.last_updated||"N/A"})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"text-green-700",children:"WordPress Requirements:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-900",children:["WP ",f.requires||"N/A"," | Tested up to"," ",f.tested||"N/A"," | PHP"," ",f.requires_php||"N/A"]})]})]})]}),!f&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Step 1:"}),' Enter a plugin slug and click "Fetch" to retrieve plugin information from WordPress API.',e.jsx("br",{}),e.jsx("strong",{children:"Step 2:"}),' Once plugin data is displayed, click "Add Plugin" to add it to your dashboard.']})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{b(!1),i(null),c(""),le()},disabled:A,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),e.jsx("button",{onClick:ye,disabled:A||!f,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:A?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Adding..."})]}):e.jsx("span",{children:"Add Plugin"})})]})]})}),e.jsx(ge,{isOpen:P,onClose:()=>{B(!1),D(null)},title:"Confirm Delete",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(De,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Delete Plugin"}),e.jsxs("p",{className:"text-gray-600",children:['Are you sure you want to remove "',(E==null?void 0:E.displayName)||(E==null?void 0:E.name),'" from your added plugins?']})]})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:[e.jsx("strong",{children:"Warning:"})," This action cannot be undone. The plugin will be removed from your dashboard and you'll need to add it again if you want to track it."]})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{B(!1),D(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsxs("button",{onClick:be,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[e.jsx(De,{className:"h-4 w-4"}),e.jsx("span",{children:"Delete Plugin"})]})]})]})})]})},xt=()=>{var E,D,A;const[s,n]=r.useState([]),[a,b]=r.useState(""),[o,c]=r.useState("7"),[m,w]=r.useState({start:"",end:""}),[$,N]=r.useState([]),[y,_]=r.useState(!1),[S,v]=r.useState(!1);console.log("Chart data: ",$);const f=g=>{var V;const{cx:p,cy:k,payload:C}=g;if(!a||!C)return null;const L=((V=s.find(G=>G.pluginSlug===a))==null?void 0:V.displayName)||a,T=C[`${L}_trend`],O=C[L];if(O==null)return null;let I="#3B82F6";return T==="improvement"?I="#10B981":T==="decline"&&(I="#EF4444"),e.jsx("circle",{cx:p,cy:k,r:5,fill:I,stroke:I,strokeWidth:2})},i=async()=>{var g;try{const p=localStorage.getItem("adminToken"),C=await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${p}`,"Content-Type":"application/json"}});if(!C.ok)throw new Error(`HTTP error! status: ${C.status}`);const L=await C.json();L.success?(n(L.plugins),console.log(`✅ Loaded ${((g=L.plugins)==null?void 0:g.length)||0} plugins from plugininformations collection (${L.pluginsWithRankHistory||0} with rank history)`)):console.warn("Failed to load plugins:",L.message)}catch(p){console.error("Error loading plugins from plugininformations collection:",p),p.name==="TypeError"&&p.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins from database","error")}},j=async()=>{if(!a){N([]);return}try{_(!0);const g=s.find(k=>k.pluginSlug===a);if(!g){console.error("Selected plugin not found in loaded plugins"),window.toast("Selected plugin not found","error"),N([]);return}if(!g.rankHistory||!Array.isArray(g.rankHistory)){console.log(`Plugin ${a} has no rank history data`),N([]);return}console.log(`📊 Processing rank history for ${a}: ${g.rankHistory.length} entries`);const p=R(g);N(p)}catch(g){console.error("Error loading chart data:",g),window.toast("Failed to load chart data","error")}finally{_(!1)}},R=g=>{if(!g.rankHistory||!Array.isArray(g.rankHistory))return[];const p=g.displayName||g.pluginName||g.pluginSlug;let k=g.rankHistory;if(o!=="custom"){const T=parseInt(o),O=new Date;O.setDate(O.getDate()-T),k=g.rankHistory.filter(I=>{const[V,G,J]=I.date.split("-");return new Date(J,G-1,V)>=O})}else if(m.start&&m.end){const T=new Date(m.start),O=new Date(m.end);k=g.rankHistory.filter(I=>{const[V,G,J]=I.date.split("-"),ee=new Date(J,G-1,V);return ee>=T&&ee<=O})}return k.sort((T,O)=>{const[I,V,G]=T.date.split("-"),[J,ee,re]=O.date.split("-"),le=new Date(G,V-1,I),ne=new Date(re,ee-1,J);return le-ne}).map((T,O,I)=>{const V=I[O-1];let G="stable";return V&&(T.previousRank<V.previousRank?G="improvement":T.previousRank>V.previousRank&&(G="decline")),{date:T.date,[p]:T.previousRank,[`${p}_trend`]:G}})},P=async()=>{if(!a){window.toast("Please select a plugin first","warning");return}try{_(!0),window.toast("Refreshing chart data...","info"),await j(),window.toast("Chart data refreshed successfully","success")}catch(g){console.error("Error refreshing chart data:",g),window.toast("Failed to refresh chart data","error")}finally{_(!1)}};r.useEffect(()=>{i()},[]),r.useEffect(()=>{j()},[a,o,m]);const B=g=>{c(g),v(g==="custom")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(je,{className:"h-8 w-8 text-blue-600 mr-3"}),"Plugin Rank Analysis"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track ranking trends for your added plugins"})]})})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plugin"}),e.jsxs("select",{value:a,onChange:g=>b(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(g=>e.jsx("option",{value:g.pluginSlug,children:g.displayName},g.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:o,onChange:g=>B(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom range"})]})]}),S&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:m.start,onChange:g=>w(p=>({...p,start:g.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:m.end,onChange:g=>w(p=>({...p,end:g.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:P,disabled:y||!a,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Fetch latest rank from WordPress and save to database",children:[e.jsx(ae,{className:`h-4 w-4 mr-2 ${y?"animate-spin":""}`}),"Refresh"]})})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:["Plugin Rank Trends",a&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",((E=s.find(g=>g.pluginSlug===a))==null?void 0:E.displayName)||a,")"]})]}),y?e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading chart data..."})]})}):$.length>0?e.jsx("div",{className:"h-96",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(Je,{data:$,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(Ee,{tick:{fontSize:12},domain:(()=>{var G;if($.length===0)return[1,100];const g=((G=s.find(J=>J.pluginSlug===a))==null?void 0:G.displayName)||a,p=$.map(J=>J[g]).filter(J=>J!=null);if(p.length===0)return[1,100];const k=Math.min(...p),C=Math.max(...p),L=p[p.length-1],T=Math.max(1,L-10),O=L+10,I=Math.min(T,k-2),V=Math.max(O,C+2);return[I,V]})(),reversed:!0,label:{value:"Rank",angle:-90,position:"insideLeft"},allowDecimals:!1,type:"number"}),e.jsx(ke,{labelFormatter:g=>`Date: ${g}`,formatter:(g,p)=>[g?`#${g}`:"No data",p]}),e.jsx(Ws,{}),a&&e.jsx(Ye,{type:"monotone",dataKey:((D=s.find(g=>g.pluginSlug===a))==null?void 0:D.displayName)||a,stroke:"#3B82F6",strokeWidth:2,dot:e.jsx(f,{}),connectNulls:!1,activeDot:{r:6,stroke:"#3B82F6",strokeWidth:2},children:e.jsx(Te,{dataKey:((A=s.find(g=>g.pluginSlug===a))==null?void 0:A.displayName)||a,position:"top",fontSize:10,fill:"#374151",formatter:g=>g?`#${g}`:""})},a)]})})}):e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(je,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Data Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:a?"No rank history found for the selected plugin and date range":"Select a plugin to view its rank trends"}),s.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Add plugins from the Dashboard first to see their ranking trends."})]})})]})]})},hs=s=>{const n=document.createElement("textarea");return n.innerHTML=s,n.value},ht=(s,n=40)=>{const a=hs(s);if(a.length<=n)return a;const b=a.split(" ");let o=b[0];for(let c=1;c<b.length&&(o+" "+b[c]).length<=n-3;c++)o+=" "+b[c];return o+"..."},gt=()=>{var xe;const[s,n]=r.useState("performance"),[a,b]=r.useState([]),[o,c]=r.useState(""),[m,w]=r.useState([]),[$,N]=r.useState(!1),[y,_]=r.useState(!1),[S,v]=r.useState(""),[f,i]=r.useState(""),[j,R]=r.useState(!1),[P,B]=r.useState(new Set),[E,D]=r.useState(!1),[A,g]=r.useState(new Set),[p,k]=r.useState(null),[C,L]=r.useState(!1),[T,O]=r.useState([]),[I,V]=r.useState(!1),[G,J]=r.useState(!1),[ee,re]=r.useState(""),[le,ne]=r.useState({}),[ye,me]=r.useState(!1),[be,oe]=r.useState(!1),[se,te]=r.useState(null),[ue,x]=r.useState([]),[U,Q]=r.useState([]),[t,h]=r.useState(!1),[d,u]=r.useState(!1),M=async()=>{var l;try{const z=localStorage.getItem("adminToken"),K=await(await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();K.success?(b(K.plugins),console.log(`✅ Loaded ${((l=K.plugins)==null?void 0:l.length)||0} plugins from plugininformations collection for keyword analysis`)):console.error("Failed to load plugins:",K.message)}catch(z){console.error("Error loading plugins from plugininformations collection:",z),window.toast("Failed to load plugins from database","error")}},F=async()=>{try{if(N(!0),!o){w([]),ne({}),N(!1);return}const l=localStorage.getItem("adminToken"),W=`https://pluginsight.vercel.app/api/keywords?pluginSlug=${o}`,K=await(await fetch(W,{headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}})).json();if(K.success){const X=K.keywords.map(he=>({...he,position:he.latestRank,lastChecked:he.lastChecked||he.updatedAt}));w(X);const Z={};X.forEach(he=>{Z[he._id]=he.occurrences||0}),ne(Z)}else console.error("Failed to load keywords:",K.message),window.toast(K.message||"Failed to load keywords","error"),w([]),ne({})}catch(l){console.error("Error loading keywords:",l),window.toast("Failed to load keywords","error"),w([]),ne({})}finally{N(!1)}},q=async()=>{if(!f||!S.trim()){window.toast("Please select a plugin and enter a keyword","error");return}try{const l=localStorage.getItem("adminToken"),z=a.find(X=>X.pluginSlug===f),K=await(await fetch("https://pluginsight.vercel.app/api/keywords",{method:"POST",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:f,pluginName:(z==null?void 0:z.displayName)||f,keyword:S.trim()})})).json();K.success?(window.toast("Keyword added successfully","success"),v(""),i(""),_(!1),f===o&&F()):window.toast(K.message||"Failed to add keyword","error")}catch(l){console.error("Error adding keyword:",l),window.toast("Failed to add keyword","error")}},Se=async()=>{try{R(!0),window.toast("Refreshing keyword ranks...","info");const l=localStorage.getItem("adminToken"),Y=await(await fetch("https://pluginsight.vercel.app/api/keywords/refresh-ranks",{method:"POST",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}})).json();Y.success?(window.toast(Y.message,"success"),await F()):window.toast(Y.message||"Failed to refresh keyword ranks","error")}catch(l){console.error("Error refreshing keyword ranks:",l),window.toast("Failed to refresh keyword ranks","error")}finally{R(!1)}},we=async()=>{try{const l=localStorage.getItem("adminToken"),z="https://pluginsight.vercel.app",W=Array.from(P),K=await(await fetch(`${z}/api/keywords/bulk-delete`,{method:"DELETE",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"},body:JSON.stringify({keywordIds:W})})).json();K.success?(window.toast(`${W.length} keywords deleted successfully`,"success"),B(new Set),D(!1),F()):window.toast(K.message||"Failed to delete keywords","error")}catch(l){console.error("Error deleting keywords:",l),window.toast("Failed to delete keywords","error")}},ie=async()=>{if(p)try{const l=localStorage.getItem("adminToken"),Y=await(await fetch(`https://pluginsight.vercel.app/api/keywords/${p}`,{method:"DELETE",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}})).json();if(Y.success){window.toast("Keyword deleted successfully","success"),L(!1),k(null);const K=new Set(A);K.delete(p),g(K),F()}else window.toast(Y.message||"Failed to delete keyword","error")}catch(l){console.error("Error deleting keyword:",l),window.toast("Failed to delete keyword","error")}},H=async(l=!1)=>{try{V(!0);const z=localStorage.getItem("adminToken"),X=await(await fetch(`https://pluginsight.vercel.app/api/competitors${l?"?autoDiscover=true":""}`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();X.success&&(O(X.competitors),l&&X.competitors.length>0&&window.toast(`Discovered ${X.competitors.length} competitor plugins`,"success"))}catch(z){console.error("Error loading competitors:",z),window.toast("Failed to load competitors","error")}finally{V(!1)}},Le=async()=>{if(!ee.trim()){window.toast("Please enter a plugin slug","error");return}try{const l=localStorage.getItem("adminToken"),Y=await(await fetch("https://pluginsight.vercel.app/api/competitors",{method:"POST",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:ee.trim()})})).json();Y.success?(window.toast("Competitor added successfully","success"),re(""),J(!1),H()):window.toast(Y.message||"Failed to add competitor","error")}catch(l){console.error("Error adding competitor:",l),window.toast("Failed to add competitor","error")}},Ke=async l=>{te(l),me(!0),h(!0);try{const z=localStorage.getItem("adminToken"),K=await(await fetch(`https://pluginsight.vercel.app/api/keywords/ranks/${encodeURIComponent(l.keyword)}/${l.pluginSlug}?limit=30`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();K.success?x(K.rankHistory):(window.toast("Failed to load rank history","error"),x([]))}catch(z){console.error("Error loading rank history:",z),window.toast("Failed to load rank history","error"),x([])}finally{h(!1)}},_e=async l=>{te(l),oe(!0),u(!0);try{const W=`https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(l.keyword)}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`,K=await(await fetch(W)).json();if(K&&K.plugins){const X=K.plugins.filter(Z=>Z.slug!==l.pluginSlug).sort((Z,he)=>(he.active_installs||0)-(Z.active_installs||0)).slice(0,10).map(Z=>({pluginName:Z.name,pluginSlug:Z.slug,activeInstalls:Z.active_installs||0,rating:Z.rating||0,numRatings:Z.num_ratings||0,testedUpTo:Z.tested||"N/A",lastUpdated:Z.last_updated||"N/A",wordpressUrl:`https://wordpress.org/plugins/${Z.slug}/`}));Q(X)}else window.toast("Failed to load related plugins","error"),Q([])}catch(z){console.error("Error loading related plugins:",z),window.toast("Failed to load related plugins","error"),Q([])}finally{u(!1)}};return r.useEffect(()=>{M()},[]),r.useEffect(()=>{F()},[o]),r.useEffect(()=>{s==="competitors"&&H(!0)},[s]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6",children:e.jsx("div",{children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(pe,{className:"h-6 w-6 text-blue-600 mr-2"}),"Keyword Analysis"]})})}),e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsxs("button",{onClick:()=>n("performance"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="performance"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(je,{className:"h-4 w-4 inline mr-2"}),"Keyword Performance"]}),e.jsxs("button",{onClick:()=>n("competitors"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="competitors"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(Ce,{className:"h-4 w-4 inline mr-2"}),"Competitors"]})]})})]}),s==="performance"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-stretch md:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Plugin:"}),e.jsxs("select",{value:o,onChange:l=>c(l.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"All plugins"}),a.map(l=>e.jsx("option",{value:l.pluginSlug,children:l.displayName},l.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[P.size>0&&e.jsxs("button",{onClick:()=>D(!0),className:"flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:[e.jsx(De,{className:"h-4 w-4 mr-1"}),"Delete (",P.size,")"]}),e.jsxs("button",{onClick:()=>_(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Ne,{className:"h-4 w-4 mr-1"}),"Add"]}),e.jsxs("button",{onClick:Se,disabled:j,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(ae,{className:`h-4 w-4 mr-1 ${j?"animate-spin":""}`}),j?"Refreshing...":"Refresh Ranks"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-base font-semibold text-gray-900",children:["Keywords",o&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["for"," ",(xe=a.find(l=>l.pluginSlug===o))==null?void 0:xe.displayName]})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",m.length," keywords"]})})]}),$?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading keywords..."})]}):m.length>0?e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto max-h-[470px] overflow-y-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0 z-10",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12",children:e.jsx("input",{type:"checkbox",checked:m.every(l=>P.has(l._id))&&m.length>0,onChange:l=>{const z=new Set(P);l.target.checked?m.forEach(W=>z.add(W._id)):m.forEach(W=>z.delete(W._id)),B(z)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4",children:"Keyword"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Position"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Analytics"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Occurrences"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Tracked"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map((l,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-12",children:e.jsx("input",{type:"checkbox",checked:P.has(l._id),onChange:W=>{const Y=new Set(P);W.target.checked?Y.add(l._id):Y.delete(l._id),B(Y)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-1/4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:l.keyword})}),l.source&&e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${l.source==="manual"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:l.source})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:l.position||"-"}),l.rankChange!==null&&l.rankChange!==void 0&&e.jsxs("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${l.rankChange<0?"bg-green-100 text-green-800":l.rankChange>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[l.rankChange<0?"↑":l.rankChange>0?"↓":"=",Math.abs(l.rankChange)]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm w-20",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>Ke(l),className:"inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",title:"View Rank Analytics",children:e.jsx(je,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>_e(l),className:"inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors",title:"Search Related Plugins",children:e.jsx(Us,{className:"h-4 w-4"})})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20",children:le[l._id]||0}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:l.addedAt?new Date(l.addedAt).toLocaleDateString("en-GB"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:l.updatedAt?new Date(l.updatedAt).toLocaleDateString("en-GB"):"-"})]},l._id))})]})})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(pe,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Keywords Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:o?"No keywords added for this plugin yet.":"Please select a plugin first to view and manage keywords."}),o&&e.jsxs("button",{onClick:()=>_(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Ne,{className:"h-4 w-4 mr-2"}),"Add First Keyword"]})]})]})]}),s==="competitors"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-semibold text-gray-900",children:"Competitor Plugins"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>H(!0),disabled:I,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(ae,{className:`h-4 w-4 mr-1 ${I?"animate-spin":""}`}),I?"Discovering...":"Discover"]}),e.jsxs("button",{onClick:()=>J(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Ne,{className:"h-4 w-4 mr-1"}),"Add Manually"]})]})]}),I?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading competitors..."})]}):T.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Slug"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Rank"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:T.map((l,z)=>{var W;return e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:l.pluginName})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:l.pluginSlug})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:l.currentRank?`#${l.currentRank}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:((W=l.activeInstalls)==null?void 0:W.toLocaleString())||"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[l.tags&&l.tags.length>0?l.tags.slice(0,3).map((Y,K)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:Y},`${l._id}-tag-${K}`)):e.jsx("span",{className:"text-gray-400",children:"No tags"}),l.tags&&l.tags.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",l.tags.length-3," more"]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:new Date(l.createdAt).toLocaleDateString("en-GB")})]},l._id)})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Competitors Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add keywords to automatically discover competitor plugins, or add competitors manually."}),e.jsxs("button",{onClick:()=>J(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Ne,{className:"h-4 w-4 mr-2"}),"Add First Competitor"]})]})]}),e.jsx(ge,{isOpen:y,onClose:()=>{_(!1),v(""),i("")},title:"Add New Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Plugin"}),e.jsxs("select",{value:f,onChange:l=>i(l.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Choose a plugin..."}),a.map(l=>e.jsx("option",{value:l.pluginSlug,children:l.displayName},l.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Keyword"}),e.jsx("input",{type:"text",value:S,onChange:l=>v(l.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter keyword...",onKeyDown:l=>{l.key==="Enter"&&q()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{_(!1),v(""),i("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:q,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Keyword"})]})]})}),e.jsx(ge,{isOpen:E,onClose:()=>D(!1),title:"Delete Keywords",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:["Are you sure you want to delete ",P.size," selected keyword(s)? This action cannot be undone and will also remove all related analytics data."]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>D(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:we,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keywords"})]})]})}),e.jsx(ge,{isOpen:C,onClose:()=>{L(!1),k(null)},title:"Delete Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this keyword? This action cannot be undone and will also remove all related analytics data."}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{L(!1),k(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:ie,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keyword"})]})]})}),e.jsx(ge,{isOpen:G,onClose:()=>{J(!1),re("")},title:"Add Competitor Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsx("input",{type:"text",value:ee,onChange:l=>re(l.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter plugin slug...",onKeyDown:l=>{l.key==="Enter"&&Le()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{J(!1),re("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Le,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Competitor"})]})]})}),e.jsx(ge,{isOpen:ye,onClose:()=>{me(!1),te(null),x([])},title:`Rank Analytics - ${(se==null?void 0:se.keyword)||""}`,children:e.jsxs("div",{className:"space-y-4",children:[t?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading rank history..."})]}):ue.length>0?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Rank Trend (Last 30 days)"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Plugin:"})," ",se==null?void 0:se.pluginName]})]}),e.jsx("div",{className:"h-64",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(Je,{data:ue.slice().reverse().map(l=>({...l,displayDate:l.date,invertedRank:l.rank?-l.rank:0})),margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Re,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx($e,{dataKey:"displayDate",tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"}}),e.jsx(Ee,{domain:["dataMin","dataMax"],tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"},tickFormatter:l=>`#${Math.abs(l)}`}),e.jsx(ke,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px"},formatter:l=>[`#${Math.abs(l)}`,"Rank"],labelFormatter:l=>`Date: ${l}`}),e.jsx(Ye,{type:"monotone",dataKey:"invertedRank",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},children:e.jsx(Te,{dataKey:"invertedRank",position:"top",formatter:l=>`#${Math.abs(l)}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Fs,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Rank History"}),e.jsx("p",{className:"text-gray-600",children:"No rank history found for this keyword."})]}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx("button",{onClick:()=>{me(!1),te(null),x([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})}),e.jsx(ge,{isOpen:be,onClose:()=>{oe(!1),te(null),Q([])},title:`Related Plugins - "${(se==null?void 0:se.keyword)||""}"`,maxWidth:"max-w-6xl",fixedHeight:!0,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:d?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading related plugins..."})]}):U.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tested Up To"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:U.map((l,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap",children:[e.jsxs("a",{href:l.wordpressUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 font-medium flex items-center",title:hs(l.pluginName),children:[ht(l.pluginName),e.jsx(Me,{className:"h-3 w-3 ml-1"})]}),e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:l.pluginSlug})]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:l.activeInstalls>0?l.activeInstalls.toLocaleString():"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsxs("span",{children:[l.rating>0?l.rating.toFixed(1):"N/A",l.numRatings>0&&e.jsxs("span",{className:"text-xs text-gray-400 ml-1",children:["(",l.numRatings,")"]})]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:l.testedUpTo!=="N/A"?`WP ${l.testedUpTo}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:l.lastUpdated!=="N/A"?(()=>{const W=new Date(l.lastUpdated);if(isNaN(W.getTime()))return"N/A";const Y=W.getDate().toString().padStart(2,"0"),K=(W.getMonth()+1).toString().padStart(2,"0"),X=W.getFullYear();return`${K}-${Y}-${X}`})():"N/A"})]},l.pluginSlug))})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(pe,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Related Plugins Found"}),e.jsx("p",{className:"text-gray-600",children:"No plugins found containing this keyword."})]})}),e.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200 flex-shrink-0",children:e.jsx("button",{onClick:()=>{oe(!1),te(null),Q([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})})]})},pt=()=>{const[s,n]=r.useState("downloaded-data"),[a,b]=r.useState([]),[o,c]=r.useState(!1),[m,w]=r.useState(""),[$,N]=r.useState([]),[y,_]=r.useState(!1),[S,v]=r.useState("15"),[f,i]=r.useState({start:"",end:""}),[j,R]=r.useState(""),[P,B]=r.useState([]),[E,D]=r.useState(!1),[A,g]=r.useState({startDate:"",endDate:"",rating:[],page:1}),[p,k]=r.useState({totalReviews:0,averageRating:0,ratingDistribution:{}});r.useEffect(()=>{C()},[]);const C=async()=>{try{c(!0);const T=localStorage.getItem("adminToken"),I=await fetch("https://pluginsight.vercel.app/api/analytics/added-plugins",{headers:{Authorization:`Bearer ${T}`,"Content-Type":"application/json"}});if(!I.ok)throw new Error(`HTTP error! status: ${I.status}`);const V=await I.json();V.success?b(V.plugins):console.warn("Failed to load added plugins:",V.message)}catch(T){console.error("Error loading added plugins:",T),T.name==="TypeError"&&T.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins","error")}finally{c(!1)}},L=[{id:"downloaded-data",name:"Downloaded Data",icon:de},{id:"plugin-reviews",name:`Plugin Reviews${p.totalReviews>0?` (${p.totalReviews})`:""}`,icon:Ve}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(ts,{className:"h-6 w-6 text-blue-600 mr-2"}),"Plugin Data Analysis"]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8 px-6",children:L.map(T=>{const O=T.icon;return e.jsxs("button",{onClick:()=>n(T.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${s===T.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{children:T.name})]},T.id)})})}),e.jsxs("div",{className:"p-6",children:[s==="downloaded-data"&&e.jsx(ft,{addedPlugins:a,selectedPlugin:m,setSelectedPlugin:w,downloadData:$,setDownloadData:N,downloadLoading:y,setDownloadLoading:_,dateRange:S,setDateRange:v,customDateRange:f,setCustomDateRange:i}),s==="plugin-reviews"&&e.jsx(yt,{addedPlugins:a,reviewsPlugin:j,setReviewsPlugin:R,reviews:P,setReviews:B,reviewsLoading:E,setReviewsLoading:D,reviewFilters:A,setReviewFilters:g,reviewStats:p,setReviewStats:k})]})]})]})},ft=({addedPlugins:s,selectedPlugin:n,setSelectedPlugin:a,downloadData:b,setDownloadData:o,downloadLoading:c,setDownloadLoading:m,dateRange:w,setDateRange:$,customDateRange:N,setCustomDateRange:y})=>{var v;const _=async()=>{try{m(!0),window.toast("Starting plugin download data fetch...","info");const f=localStorage.getItem("adminToken"),R=await(await fetch("https://pluginsight.vercel.app/api/analytics/download-data/refresh",{method:"POST",headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}})).json();R.success?(window.toast(R.message,"success"),n&&S(n)):window.toast(R.message||"Failed to refresh download data","error")}catch(f){console.error("Error refreshing download data:",f),window.toast("Failed to refresh download data","error")}finally{m(!1)}},S=async f=>{if(f)try{m(!0);const i=localStorage.getItem("adminToken"),j="https://pluginsight.vercel.app";let R=`${j}/api/analytics/download-data/${f}?days=${w}`;w==="custom"&&N.start&&N.end&&(R=`${j}/api/analytics/download-data/${f}?startDate=${N.start}&endDate=${N.end}`);const B=await(await fetch(R,{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();if(B.success){const E=B.downloadData.map(D=>({date:new Date(D.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:D.downloads,fullDate:D.date}));o(E)}}catch(i){console.error("Error loading download data:",i),window.toast("Failed to load download data","error")}finally{m(!1)}};return qe.useEffect(()=>{n&&S(n)},[n,w,N]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:n,onChange:f=>a(f.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(f=>e.jsx("option",{value:f.pluginSlug,children:f.displayName||f.pluginName},f.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:w,onChange:f=>$(f.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),w==="custom"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:N.start,onChange:f=>y(i=>({...i,start:f.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:N.end,onChange:f=>y(i=>({...i,end:f.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("button",{onClick:_,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(ae,{className:`h-4 w-4 mr-2 ${c?"animate-spin":""}`}),"Refresh"]})]}),n&&b.length>0?e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Download Trends -"," ",((v=s.find(f=>f.pluginSlug===n))==null?void 0:v.displayName)||n]}),e.jsx("div",{className:"h-96",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(os,{data:b,children:[e.jsx(Re,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx($e,{dataKey:"date",stroke:"#6b7280",fontSize:12}),e.jsx(Ee,{stroke:"#6b7280",fontSize:12,tickFormatter:f=>f.toLocaleString()}),e.jsx(ke,{contentStyle:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"8px"},formatter:f=>[f.toLocaleString(),"Downloads"],labelFormatter:(f,i)=>i&&i[0]?new Date(i[0].payload.fullDate).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):f}),e.jsx(is,{dataKey:"downloads",fill:"#3b82f6",radius:[4,4,0,0],children:e.jsx(Te,{dataKey:"downloads",position:"top",fontSize:10,fill:"#3b82f6",formatter:f=>f.toLocaleString()})})]})})})]}):n&&c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading download data..."})]})}):n?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(de,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Download Data"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No download data found for this plugin. Click refresh to fetch the latest data."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(de,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its download trends."})]})})]})},yt=({addedPlugins:s,reviewsPlugin:n,setReviewsPlugin:a,reviews:b,setReviews:o,reviewsLoading:c,setReviewsLoading:m,reviewFilters:w,setReviewFilters:$,reviewStats:N,setReviewStats:y})=>{const _=async()=>{try{m(!0),window.toast("Starting plugin reviews fetch...","info");const i=localStorage.getItem("adminToken"),R=await fetch("https://pluginsight.vercel.app/api/analytics/reviews/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!R.ok)throw new Error(`HTTP error! status: ${R.status}`);const P=await R.json();P.success?(window.toast(P.message,"success"),n&&S(n)):window.toast(P.message||"Failed to refresh reviews","error")}catch(i){console.error("Error refreshing reviews:",i),i.name==="TypeError"&&i.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to refresh reviews","error")}finally{m(!1)}},S=async(i,j=1)=>{var R;if(i)try{m(!0);const P=new URLSearchParams({page:j.toString(),limit:"20"});w.startDate&&P.append("startDate",w.startDate),w.endDate&&P.append("endDate",w.endDate),w.rating.length>0&&w.rating.forEach(D=>P.append("rating",D.toString())),console.log(`Loading reviews for plugin: ${i} with params:`,P.toString());const B=await Be(`/api/analytics/reviews/${i}?${P}`);if(!B.ok)throw new Error(`HTTP error! status: ${B.status}`);const E=await B.json();console.log("Reviews API response:",E),E.success?(console.log(`Loaded ${((R=E.reviews)==null?void 0:R.length)||0} reviews for ${i} (page ${j})`),o(j===1?E.reviews||[]:D=>[...D,...E.reviews||[]]),y(E.stats||{})):(console.warn("Reviews API returned success: false",E),j===1&&o([]),y({}))}catch(P){console.error("Error loading reviews:",P),P.name==="TypeError"&&P.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast(`Failed to load reviews: ${P.message}`,"error"),j===1&&(o([]),y({}))}finally{m(!1)}};qe.useEffect(()=>{n&&S(n)},[n,w]);const v=i=>Array.from({length:5},(j,R)=>e.jsx("span",{className:`text-lg ${R<i?"text-yellow-400":"text-gray-300"}`,children:"★"},R)),f=i=>{if(!i)return"";let j=i.replace(/<!\[CDATA\[/g,"").replace(/\]\]>/g,"").trim();j=j.replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/^.*?Replies:\s*\d+\s*/gi,"").replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*/gi,"").replace(/Rating:\s*\d+\s*stars?\s*/gi,"").trim();const R=document.createElement("textarea");return R.innerHTML=j,j=R.value,j=j.replace(/<[^>]*>/g,""),j=j.replace(/\s+/g," ").trim(),j};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:n,onChange:i=>a(i.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(i=>e.jsx("option",{value:i.pluginSlug,children:i.displayName||i.pluginName},i.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:w.startDate,onChange:i=>$(j=>({...j,startDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:w.endDate,onChange:i=>$(j=>({...j,endDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating Filter"}),e.jsxs("select",{value:w.rating.length===1?w.rating[0]:"",onChange:i=>{const j=i.target.value;$(j===""?R=>({...R,rating:[]}):R=>({...R,rating:[parseInt(j)]}))},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All ratings"}),e.jsx("option",{value:"5",children:"5 stars"}),e.jsx("option",{value:"4",children:"4 stars"}),e.jsx("option",{value:"3",children:"3 stars"}),e.jsx("option",{value:"2",children:"2 stars"}),e.jsx("option",{value:"1",children:"1 star"})]})]})]}),e.jsxs("button",{onClick:_,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(ae,{className:`h-4 w-4 mr-2 ${c?"animate-spin":""}`}),"Refresh"]})]}),n&&b.length>0?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"text-sm text-gray-600 flex items-center gap-2",children:["Total",e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:N.totalReviews}),"Reviews"]})})})}),e.jsx("div",{className:"h-[460px] overflow-y-auto space-y-4",children:b.map((i,j)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 animate-fade-in-up",style:{animationDelay:`${j*100}ms`,animationFillMode:"both"},children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex",children:v(i.rating)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["by ",i.author," •"," ",new Date(i.date).toLocaleDateString("en-GB")]})]}),i.reviewUrl&&e.jsx("a",{href:i.reviewUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-blue-500 rounded-lg hover:text-blue-700 transition-all duration-200",children:e.jsx(Me,{className:"h-4 w-4"})})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:i.title}),e.jsx("div",{className:"text-gray-700 leading-relaxed",children:f(i.content)})]},i._id||j))})]}):n&&c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ae,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):n?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Reviews Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews found for this plugin. Click refresh to fetch the latest reviews."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its reviews."})]})})]})},bt=()=>{const{user:s}=fe(),[n,a]=r.useState([]),[b,o]=r.useState(!0),[c,m]=r.useState(""),[w,$]=r.useState(""),[N,y]=r.useState(""),[_,S]=r.useState(!1),[v,f]=r.useState(!1),[i,j]=r.useState(!1),[R,P]=r.useState(!1),[B,E]=r.useState(!1),[D,A]=r.useState(null),[g,p]=r.useState({current:1,pages:1,total:0,limit:10}),[k,C]=r.useState({name:"",email:"",password:"",role:"member"}),[L,T]=r.useState({newPassword:"",confirmPassword:""}),[O,I]=r.useState({canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1}),V=async(x=1,U="",Q="")=>{try{o(!0);const t=localStorage.getItem("adminToken");if(!t){m("Authentication token not found. Please login again.");return}const h=new URLSearchParams({page:x.toString(),limit:"10",...U&&{search:U},...Q&&{role:Q}}),u=await fetch(`https://pluginsight.vercel.app/api/users?${h}`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!u.ok){if(u.status===401){m("Authentication failed. Please login again."),localStorage.removeItem("adminToken");return}throw new Error(`HTTP error! status: ${u.status}`)}const M=await u.json();M.success?(a(M.users),p(M.pagination),m("")):m(M.message||"Failed to fetch users")}catch(t){console.error("Fetch users error:",t),t.name==="TypeError"&&t.message.includes("Failed to fetch")?m("Unable to connect to server. Please check if the backend is running."):m("Failed to fetch users. Please try again.")}finally{o(!1)}};r.useEffect(()=>{V()},[]);const G=()=>{V(1,w,N)},J=async x=>{x.preventDefault();try{const U=localStorage.getItem("adminToken"),t=await fetch("https://pluginsight.vercel.app/api/users",{method:"POST",headers:{Authorization:`Bearer ${U}`,"Content-Type":"application/json"},body:JSON.stringify(k)});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const h=await t.json();h.success?(S(!1),C({name:"",email:"",password:"",role:"member"}),V(g.current,w,N)):m(h.message||"Failed to create user")}catch(U){console.error("Add user error:",U),U.name==="TypeError"&&U.message.includes("Failed to fetch")?m("Unable to connect to server. Please check if the backend is running."):m("Failed to create user")}},ee=async x=>{x.preventDefault();try{const U=localStorage.getItem("adminToken"),t=await fetch(`https://pluginsight.vercel.app/api/users/${D._id}`,{method:"PUT",headers:{Authorization:`Bearer ${U}`,"Content-Type":"application/json"},body:JSON.stringify({name:k.name,email:k.email,role:k.role,isActive:k.isActive})});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const h=await t.json();h.success?(f(!1),A(null),V(g.current,w,N)):m(h.message||"Failed to update user")}catch(U){console.error("Edit user error:",U),U.name==="TypeError"&&U.message.includes("Failed to fetch")?m("Unable to connect to server. Please check if the backend is running."):m("Failed to update user")}},re=x=>{if(s&&s._id===x._id){window.toast("You cannot delete your own account","error");return}A(x),P(!0)},le=async()=>{if(D)try{const x=localStorage.getItem("adminToken"),Q=await fetch(`https://pluginsight.vercel.app/api/users/${D._id}`,{method:"DELETE",headers:{Authorization:`Bearer ${x}`,"Content-Type":"application/json"}});if(!Q.ok)throw new Error(`HTTP error! status: ${Q.status}`);const t=await Q.json();t.success?(V(g.current,w,N),P(!1),A(null)):m(t.message||"Failed to delete user")}catch(x){console.error("Delete user error:",x),x.name==="TypeError"&&x.message.includes("Failed to fetch")?m("Unable to connect to server. Please check if the backend is running."):m("Failed to delete user")}},ne=async x=>{if(x.preventDefault(),L.newPassword!==L.confirmPassword){m("Passwords do not match");return}try{const U=localStorage.getItem("adminToken"),t=await fetch(`https://pluginsight.vercel.app/api/users/${D._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${U}`,"Content-Type":"application/json"},body:JSON.stringify({newPassword:L.newPassword})});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const h=await t.json();h.success?(j(!1),A(null),T({newPassword:"",confirmPassword:""}),alert("Password reset successfully")):m(h.message||"Failed to reset password")}catch(U){console.error("Reset password error:",U),U.name==="TypeError"&&U.message.includes("Failed to fetch")?m("Unable to connect to server. Please check if the backend is running."):m("Failed to reset password")}},ye=x=>{A(x),C({name:x.name,email:x.email,role:x.role,isActive:x.isActive}),f(!0)},me=x=>{A(x),T({newPassword:"",confirmPassword:""}),j(!0)},be=x=>{var U,Q,t,h;A(x),I({canAddPlugins:((U=x.permissions)==null?void 0:U.canAddPlugins)||!1,canDeletePlugins:((Q=x.permissions)==null?void 0:Q.canDeletePlugins)||!1,canAddKeywords:((t=x.permissions)==null?void 0:t.canAddKeywords)||!1,canAddUsers:((h=x.permissions)==null?void 0:h.canAddUsers)||!1}),E(!0)},oe=x=>{switch(x){case"superadmin":return"bg-red-100 text-red-800";case"admin":return"bg-blue-100 text-blue-800";case"member":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},se=x=>s.role==="superadmin"||s.role==="admin"&&x.role==="member"?s._id!==x._id:!1,te=x=>!(s.role==="admin"&&x.role==="superadmin"),ue=x=>s._id===x._id?!1:s.role==="superadmin"||s.role==="admin"&&x.role==="member";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center p-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Bs,{className:"h-6 w-6 text-blue-600 mr-2"}),"Team Members"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage system users and their permissions"})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex justify-between items-center",children:[e.jsx("div",{children:["admin","superadmin"].includes(s==null?void 0:s.role)&&e.jsxs("button",{onClick:()=>S(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(Ne,{className:"h-4 w-4"}),"Add User"]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:w,onChange:x=>$(x.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Is,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsxs("select",{value:N,onChange:x=>y(x.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"})]})]}),e.jsx("button",{onClick:G,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Search"})]})]}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:c}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:b?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Loading users..."})]}):n.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:n.filter(te).map(x=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:x.name}),e.jsx("div",{className:"text-sm text-gray-500",children:x.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${oe(x.role)}`,children:x.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${x.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:x.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(x.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("div",{className:"flex items-center gap-2",children:se(x)&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>ye(x),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:e.jsx(ns,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>me(x),className:"text-yellow-600 hover:text-yellow-900",title:"Reset Password",children:e.jsx(Ms,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>be(x),className:"text-purple-600 hover:text-purple-900",title:"Manage Permissions",children:e.jsx(We,{className:"h-4 w-4"})}),ue(x)&&e.jsx("button",{onClick:()=>re(x),className:"text-red-600 hover:text-red-900",title:"Delete User",children:e.jsx(De,{className:"h-4 w-4"})})]})})})]},x._id))})]})})}),g.pages>1&&e.jsxs("div",{className:"flex justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>V(g.current-1,w,N),disabled:g.current===1,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",g.current," of ",g.pages]}),e.jsx("button",{onClick:()=>V(g.current+1,w,N),disabled:g.current===g.pages,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Next"})]}),_&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Add New User"}),e.jsxs("form",{onSubmit:J,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:k.name,onChange:x=>C({...k,name:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:k.email,onChange:x=>C({...k,email:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{type:"password",value:k.password,onChange:x=>C({...k,password:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:k.role,onChange:x=>C({...k,role:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{S(!1),C({name:"",email:"",password:"",role:"member"})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Add User"})]})]})]})}),v&&D&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Edit User"}),e.jsxs("form",{onSubmit:ee,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:k.name,onChange:x=>C({...k,name:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:k.email,onChange:x=>C({...k,email:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:k.role,onChange:x=>C({...k,role:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:(s==null?void 0:s.role)==="admin"&&["admin","superadmin"].includes(D.role),children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:k.isActive,onChange:x=>C({...k,isActive:x.target.checked}),className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{f(!1),A(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Update User"})]})]})]})}),i&&D&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Reset Password"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Reset password for: ",e.jsx("strong",{children:D.name})]}),e.jsxs("form",{onSubmit:ne,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx("input",{type:"password",value:L.newPassword,onChange:x=>T({...L,newPassword:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",value:L.confirmPassword,onChange:x=>T({...L,confirmPassword:x.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{j(!1),A(null),T({newPassword:"",confirmPassword:""})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Reset Password"})]})]})]})}),R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Os,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Delete User"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user? This action cannot be undone."})]})]}),D&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:D.name}),e.jsx("p",{className:"text-sm text-gray-500",children:D.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${oe(D.role)}`,children:D.role})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{P(!1),A(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:le,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete User"})]})]})}),e.jsx(ge,{isOpen:B,onClose:()=>{E(!1),A(null)},title:"Manage User Permissions",children:D&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:D.name}),e.jsx("p",{className:"text-sm text-gray-500",children:D.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${oe(D.role)}`,children:D.role})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Plugins"}),e.jsx("input",{type:"checkbox",checked:O.canAddPlugins,onChange:x=>I(U=>({...U,canAddPlugins:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Delete Plugins"}),e.jsx("input",{type:"checkbox",checked:O.canDeletePlugins,onChange:x=>I(U=>({...U,canDeletePlugins:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Keywords"}),e.jsx("input",{type:"checkbox",checked:O.canAddKeywords,onChange:x=>I(U=>({...U,canAddKeywords:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Users"}),e.jsx("input",{type:"checkbox",checked:O.canAddUsers,onChange:x=>I(U=>({...U,canAddUsers:x.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>{E(!1),A(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>{window.toast("Permissions updated successfully","success"),E(!1),A(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Save Permissions"})]})]})})]})},jt=()=>{const{user:s}=fe(),[n,a]=r.useState(!1),[b,o]=r.useState(""),[c,m]=r.useState(""),[w,$]=r.useState({member:{canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1},admin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0},superadmin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0}});r.useEffect(()=>{N()},[]);const N=async()=>{try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();P.success&&$(P.permissions)}catch(i){console.error("Error loading permissions:",i)}},y=(i,j,R)=>{$(P=>({...P,[i]:{...P[i],[j]:R}}))},_=async()=>{a(!0),m(""),o("");try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify({permissions:w})})).json();P.success?(o("Permissions updated successfully"),setTimeout(()=>o(""),3e3)):m(P.message||"Failed to update permissions")}catch(i){console.error("Error saving permissions:",i),m("Failed to update permissions")}finally{a(!1)}},S={canAddPlugins:"Add Plugins",canDeletePlugins:"Delete Plugins",canAddKeywords:"Add Keywords",canAddUsers:"Add Users"},v={member:"Member",admin:"Admin",superadmin:"Super Admin"};if(!s)return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-white rounded-lg p-8 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Fe,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600",children:"Please login to access settings."})]})})});const f=["admin","superadmin"].includes(s.role);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(We,{className:"h-8 w-8 text-blue-600 mr-3"}),"Settings"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Manage application settings and user permissions"})]})})}),b&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:b}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:c}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 w-1/2",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Ce,{className:"h-5 w-5 text-gray-600 mr-2"}),"User Permissions"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Configure what actions each user role can perform"})]}),e.jsx("div",{className:"flex justify-end",children:f?e.jsxs("button",{onClick:_,disabled:n,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[e.jsx(ls,{className:"h-4 w-4 mr-2"}),n?"Saving...":"Save"]}):e.jsx("div",{className:"text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg",children:"View Only - Admin privileges required to edit"})})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Permission"}),Object.keys(v).map(i=>e.jsx("th",{className:"text-center py-3 px-4 font-medium text-gray-900",children:v[i]},i))]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:Object.keys(S).map(i=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-4 px-4 text-sm font-medium text-gray-900",children:S[i]}),Object.keys(v).map(j=>{var R;return e.jsx("td",{className:"py-4 px-4 text-center",children:e.jsx("input",{type:"checkbox",checked:((R=w[j])==null?void 0:R[i])||!1,onChange:P=>y(j,i,P.target.checked),disabled:!f||j==="superadmin",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"})},j)})]},i))})]})})})]})]})},wt=()=>{const{user:s}=fe(),[n,a]=r.useState(!1),[b,o]=r.useState(!1),[c,m]=r.useState({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),w=async S=>{S.preventDefault(),o(!0);try{if(c.newPassword){if(c.newPassword!==c.confirmPassword){window.toast("New passwords do not match","error"),o(!1);return}if(c.newPassword.length<6){window.toast("Password must be at least 6 characters","error"),o(!1);return}}const v=localStorage.getItem("adminToken"),f="https://pluginsight.vercel.app",i={name:c.name,email:c.email},j=await fetch(`${f}/api/users/${s.id||s._id}`,{method:"PUT",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!j.ok)throw new Error(`HTTP error! status: ${j.status}`);const R=await j.json();if(!R.success){window.toast(R.message||"Failed to update profile","error"),o(!1);return}if(c.newPassword){const B={newPassword:c.newPassword},E=await fetch(`${f}/api/users/${s.id||s._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify(B)});if(!E.ok)throw new Error(`HTTP error! status: ${E.status}`);const D=await E.json();if(!D.success){window.toast(D.message||"Failed to update password","error"),o(!1);return}}const P={...s,...R.user};localStorage.setItem("adminUser",JSON.stringify(P)),window.toast("Profile updated successfully","success"),a(!1),m(B=>({...B,name:P.name,email:P.email,newPassword:"",confirmPassword:""})),window.location.reload()}catch(v){console.error("Error updating profile:",v),v.name==="TypeError"&&v.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to update profile","error")}finally{o(!1)}},$=()=>{m({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),a(!1)},N=S=>{m({...c,[S.target.name]:S.target.value})},y=S=>{switch(S){case"superadmin":return e.jsx(Fe,{className:"h-5 w-5 text-yellow-600"});case"admin":return e.jsx(Fe,{className:"h-5 w-5 text-blue-600"});default:return e.jsx(Ae,{className:"h-5 w-5 text-gray-600"})}},_=S=>{const v={superadmin:"bg-yellow-100 text-yellow-800 border-yellow-200",admin:"bg-blue-100 text-blue-800 border-blue-200",member:"bg-gray-100 text-gray-800 border-gray-200"};return e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${v[S]}`,children:[y(S),e.jsx("span",{className:"ml-2 capitalize",children:S})]})};return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your account information and preferences"})]}),e.jsx("div",{className:"",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-24 w-24 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4",children:e.jsx(Ae,{className:"h-12 w-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:s==null?void 0:s.name}),e.jsx("p",{className:"text-blue-100 mb-4",children:s==null?void 0:s.email}),_(s==null?void 0:s.role)]})})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),n?e.jsxs("button",{onClick:$,className:"flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[e.jsx(He,{className:"h-4 w-4"}),e.jsx("span",{children:"Cancel"})]}):e.jsxs("button",{onClick:()=>a(!0),className:"flex items-center space-x-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:[e.jsx(ns,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Full Name"]}),e.jsx("input",{type:"text",name:"name",value:c.name,onChange:N,disabled:!n,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(as,{className:"h-4 w-4 mr-2"}),"Email Address"]}),e.jsx("input",{type:"email",name:"email",value:c.email,onChange:N,disabled:!n,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Fe,{className:"h-4 w-4 mr-2"}),"Role"]}),e.jsx("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",children:e.jsx("span",{className:"capitalize",children:s==null?void 0:s.role})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Role can only be changed by administrators"})]}),n&&e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsx("input",{type:"password",name:"newPassword",value:c.newPassword,onChange:N,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",value:c.confirmPassword,onChange:N,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirm new password"})]})]})]}),n&&e.jsx("div",{className:"flex justify-end pt-6",children:e.jsxs("button",{type:"submit",disabled:b,className:"flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ls,{className:"h-4 w-4"}),e.jsx("span",{children:b?"Saving...":"Save"})]})})]})]})})]})})})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})})},Nt=["p","br","strong","b","em","i","u","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","pre","code","a","img","div","span","table","thead","tbody","tr","td","th","iframe","video","source"],vt={a:["href","title","target","rel"],img:["src","alt","title","width","height","class"],iframe:["src","width","height","frameborder","allowfullscreen","title","class"],video:["src","width","height","controls","autoplay","muted","loop","poster","class"],source:["src","type"],div:["class","id"],span:["class","id"],p:["class"],h1:["class"],h2:["class"],h3:["class"],h4:["class"],h5:["class"],h6:["class"],ul:["class"],ol:["class"],li:["class"],table:["class"],thead:["class"],tbody:["class"],tr:["class"],td:["class"],th:["class"],blockquote:["class"],pre:["class"],code:["class"]};function kt(s){if(!s)return"";const n=document.createElement("div");n.innerHTML=s;function a(o){const c=o.tagName.toLowerCase();if(!Nt.includes(c)){o.remove();return}if(c==="iframe"){const N=o.getAttribute("src"),y=o.getAttribute("title")||"Video content",_=document.createElement("div");_.className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";let S="Video",v=`<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;N&&N.includes("youtube")?(S="YouTube Video",v=`<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`):N&&N.includes("vimeo")&&(S="Vimeo Video",v=`<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`),_.innerHTML=`
        <div class="flex flex-col items-center space-y-3">
          ${v}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${S}</h4>
            <p class="text-gray-600 text-sm mb-3">${y}</p>
            <a href="${N}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `,o.parentNode.replaceChild(_,o);return}const m=vt[c]||[];Array.from(o.attributes).forEach(N=>{m.includes(N.name)||o.removeAttribute(N.name)}),Array.from(o.children).forEach(N=>a(N))}return Array.from(n.children).forEach(o=>a(o)),n.innerHTML}function Ue(s){if(!s)return"";let n=s.replace(/\[video\s+([^\]]+)\]/g,(a,b)=>{const o=b.match(/src="([^"]+)"/);return o?`<video controls><source src="${o[1]}" type="video/mp4"></video>`:""}).replace(/\[youtube\s+([^\]]+)\]/g,(a,b)=>{const o=b.match(/(?:id="|v=)([^"&\s]+)/);return o?`<iframe src="https://www.youtube.com/embed/${o[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`:""}).replace(/\[vimeo\s+([^\]]+)\]/g,(a,b)=>{const o=b.match(/id="?([^"\s]+)"?/);return o?`<iframe src="https://player.vimeo.com/video/${o[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`:""}).replace(/https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,(a,b)=>`<iframe src="https://www.youtube.com/embed/${b}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,(a,b)=>`<iframe src="https://www.youtube.com/embed/${b}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g,(a,b)=>`<iframe src="https://player.vimeo.com/video/${b}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`);return kt(n)}const St=()=>{const{slug:s}=bs(),n=Oe(),[a,b]=r.useState(null),[o,c]=r.useState(null),[m,w]=r.useState(!0),[$,N]=r.useState(""),[y,_]=r.useState({}),[S,v]=r.useState(""),[f,i]=r.useState(!0),[j,R]=r.useState(!1),P=async()=>{try{i(!0);const p=await fetch(`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&slug=${a.slug}`);if(!p.ok)throw new Error("Failed to fetch plugin information");const k=await p.json();k.versions&&_(k.versions)}catch(p){console.error("Error fetching versions data:",p),_({})}finally{i(!1)}};r.useEffect(()=>{a&&P()},[a]),r.useEffect(()=>{B()},[s]);const B=async()=>{try{w(!0);const p=localStorage.getItem("adminToken"),k="https://pluginsight.vercel.app",[C,L]=await Promise.all([fetch(`${k}/api/plugins/${s}`,{headers:{Authorization:`Bearer ${p}`}}),fetch(`${k}/api/analytics/plugin-info/${s}`,{headers:{Authorization:`Bearer ${p}`}})]);if(!C.ok)throw new Error("Failed to fetch plugin details");const T=await C.json();if(!T.success){N(T.message||"Plugin not found");return}if(b(T.plugin),L.ok){const O=await L.json();O.success&&O.pluginInfo?c(O.pluginInfo):console.log("No plugin information found in database for:",s)}else console.log("Failed to fetch plugin information from database")}catch(p){console.error("Error fetching plugin details:",p),N("Failed to load plugin details")}finally{w(!1)}},E=p=>{if(!p)return"N/A";const k=p.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!k)return"N/A";const[,C,L,T]=k;return`${T}-${L}-${C}`},D=p=>{const k=Math.round((p||0)/20);return[...Array(5)].map((C,L)=>e.jsx(Ie,{className:`h-4 w-4 ${L<k?"text-yellow-400 fill-current":"text-gray-300"}`},L))},A=p=>o&&o[p]!==void 0&&o[p]!==null?o[p]:a!=null&&a.pluginData&&a.pluginData[p]!==void 0&&a.pluginData[p]!==null?a.pluginData[p]:null,g=p=>{const k=p.split(".");if(o){let C=o;for(const L of k)if(C&&typeof C=="object"&&C[L]!==void 0)C=C[L];else{C=null;break}if(C!==null)return C}if(a!=null&&a.pluginData){let C=a.pluginData;for(const L of k)if(C&&typeof C=="object"&&C[L]!==void 0)C=C[L];else{C=null;break}return C}return null};return m?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):$||!a?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-600 text-xl mb-4",children:$}),e.jsx("button",{onClick:()=>n(-1),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{onClick:()=>n(-1),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(Hs,{className:"h-5 w-5"}),e.jsx("span",{children:"Back"})]}),e.jsx("div",{className:"h-6 w-px bg-gray-300"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Plugin Details"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[A("homepage")&&e.jsxs("button",{onClick:()=>window.open(A("homepage"),"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Me,{className:"h-4 w-4"}),e.jsx("span",{children:"Home page"})]}),e.jsxs("button",{onClick:()=>window.open(`https://wordpress.org/plugins/${a.slug}/`,"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Me,{className:"h-4 w-4"}),e.jsx("span",{children:"View on WordPress.org"})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[g("icons.2x")&&e.jsx("img",{src:g("icons.2x"),alt:a.name,className:"w-16 h-16 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:a.name}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[D(A("rating")),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",A("num_ratings")||0," ratings)"]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[e.jsx(de,{className:"h-4 w-4"}),e.jsxs("span",{children:[(A("downloaded")||0).toLocaleString()," ","downloads"]})]})]})]})]})}),g("sections.description")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Description"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ue(g("sections.description"))}})]}),g("sections.installation")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Installation"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ue(g("sections.installation"))}})]}),g("sections.faq")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequently Asked Questions"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ue(g("sections.faq"))}})]}),g("sections.changelog")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Changelog"}),e.jsx("div",{className:"prose max-w-none text-gray-700 max-h-96 overflow-y-auto",dangerouslySetInnerHTML:{__html:Ue(g("sections.changelog"))}})]}),A("screenshots")&&Object.keys(A("screenshots")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Screenshots"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(A("screenshots")).slice(0,6).map(([p,k])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:k.src,alt:k.caption||`Screenshot ${p}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200",loading:"lazy"}),k.caption&&e.jsx("p",{className:"text-sm text-gray-600",children:k.caption})]},p))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plugin Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("version")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Rank"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",a.currentRank||"N/A"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:E(A("last_updated"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Added"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:E(A("added"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requires WP"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("requires")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tested up to"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("tested")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"PHP Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("requires_php")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Installs"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:A("active_installs")?`${A("active_installs").toLocaleString()}+`:"N/A"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Download"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 flex gap-1 items-center",children:["Current Version",e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Latest"})]}),e.jsxs("div",{className:"text-md font-bold text-gray-900",children:["v",A("version")||"N/A"]}),e.jsx("button",{onClick:()=>window.open(A("download_link"),"_blank"),className:"bg-green-600 hover:bg-green-700 text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:e.jsx(de,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Older Versions"}),f?e.jsx("div",{className:"border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-500",children:"Loading versions..."}):e.jsxs("select",{value:S,onChange:p=>v(p.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a version..."}),Object.entries(y).filter(([p])=>{var k;return p!==((k=a==null?void 0:a.pluginData)==null?void 0:k.version)}).sort((p,k)=>{const C=O=>O.split(".").map(Number),[L,T]=[C(p[0]),C(k[0])];for(let O=0;O<Math.max(L.length,T.length);O++){const I=(T[O]||0)-(L[O]||0);if(I!==0)return I}return 0}).slice(0,15).map(([p])=>e.jsxs("option",{value:p,children:["v",p]},p))]}),e.jsx("button",{onClick:p=>{p.preventDefault(),S&&(y!=null&&y[S])&&(R(!0),window.open(y[S],"_blank"),setTimeout(()=>R(!1),2e3))},disabled:!S||j||f,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:j?e.jsx(e.Fragment,{children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"})}):e.jsx(e.Fragment,{children:e.jsx(de,{className:"h-4 w-4"})})})]})})]}),A("donate_link")&&e.jsxs("button",{onClick:()=>window.open(A("donate_link"),"_blank"),className:"w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[e.jsx(zs,{className:"h-4 w-4"}),e.jsx("span",{children:"Donate"})]})]}),A("author")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Author"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Ae,{className:"h-8 w-8 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:A("author").replace(/<[^>]*>/g,"")}),A("author_profile")&&e.jsx("a",{href:A("author_profile"),target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800",children:"View Profile"})]})]})]}),A("tags")&&Object.keys(A("tags")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.keys(A("tags")).slice(0,10).map(p=>e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:p},p))})]}),A("contributors")&&Object.keys(A("contributors")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contributors"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:Object.entries(A("contributors")).slice(0,10).map(([p,k])=>e.jsx("a",{href:k.profile,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 rounded-lg shadow-sm",children:e.jsx("img",{src:k.avatar,alt:k.display_name||p,title:k.display_name||p,className:"w-8 h-8 rounded-full"})},p))})]})]})]})})]})};function Pt(){return e.jsx(tt,{children:e.jsx(et,{children:e.jsx(js,{children:e.jsxs(ws,{children:[e.jsx(ce,{path:"/login",element:e.jsx(it,{})}),e.jsxs(ce,{path:"/*",element:e.jsx(at,{children:e.jsx(ot,{})}),children:[e.jsx(ce,{path:"dashboard",element:e.jsx(ut,{})})," ",e.jsx(ce,{path:"plugin-rank",element:e.jsx(xt,{})}),e.jsx(ce,{path:"keyword-analysis",element:e.jsx(gt,{})}),e.jsx(ce,{path:"analytics",element:e.jsx(pt,{})}),e.jsx(ce,{path:"users",element:e.jsx(bt,{})}),e.jsx(ce,{path:"settings",element:e.jsx(jt,{})}),e.jsx(ce,{path:"profile",element:e.jsx(wt,{})}),e.jsx(ce,{path:"plugin-details/:slug",element:e.jsx(St,{})}),e.jsx(ce,{path:"",element:e.jsx(ss,{to:"/dashboard",replace:!0})})]})]})})})})}ms(document.getElementById("root")).render(e.jsx(r.StrictMode,{children:e.jsx(Pt,{})}));
