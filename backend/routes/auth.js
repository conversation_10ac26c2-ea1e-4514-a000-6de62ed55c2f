import express from "express";
import jwt from "jsonwebtoken";
import User from "../models/User.js";

const router = express.Router();

// Register
router.post("/register", async (req, res) => {
  try {
    const { email, password, name, role = "member" } = req.body;

    // Validate required fields
    if (!email || !password || !name) {
      return res.status(400).json({
        success: false,
        message: "Name, email, and password are required",
      });
    }

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User already exists",
      });
    }

    // Create user (password will be hashed automatically by the model)
    const user = new User({
      name,
      email,
      password,
      role,
    });

    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role },
      process.env.JWT_SECRET || "fallback-secret",
      { expiresIn: "30d" }
    );

    res.status(201).json({
      success: true,
      message: "User created successfully",
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
    });
  } catch (error) {
    console.error("Registration error:", error);

    // Handle validation errors
    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((err) => err.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// Login
router.post("/login", async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: "Email and password are required",
      });
    }

    // Find user
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(400).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role },
      process.env.JWT_SECRET || "fallback-secret",
      { expiresIn: "30d" }
    );

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
    });
  } catch (error) {
    console.error("Login error:", error);

    // Ensure we always return a proper JSON response
    if (!res.headersSent) {
      res.setHeader("Content-Type", "application/json");
      res.status(500).json({
        success: false,
        message: "Server error",
        error:
          process.env.NODE_ENV === "production"
            ? "Internal server error"
            : error.message,
      });
    }
  }
});

// Get current user
router.get("/me", async (req, res) => {
  try {
    const token = req.header("Authorization")?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "No token provided",
      });
    }

    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "fallback-secret"
    );
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    if (!user.isActive) {
      return res.status(400).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
        lastLogin: user.lastLogin,
      },
    });
  } catch (error) {
    console.error("Auth verification error:", error);
    res.status(401).json({
      success: false,
      message: "Invalid token",
    });
  }
});

// Initialize superadmin user (for production setup)
router.post("/init-superadmin", async (req, res) => {
  try {
    // Check if superadmin already exists
    const existingSuperAdmin = await User.findOne({
      email: "<EMAIL>",
    });

    if (existingSuperAdmin) {
      return res.json({
        success: true,
        message: "Super admin already exists",
        user: {
          email: existingSuperAdmin.email,
          name: existingSuperAdmin.name,
          role: existingSuperAdmin.role,
        },
      });
    }

    // Create superadmin user
    const superAdmin = new User({
      name: "Super Admin",
      email: "<EMAIL>",
      password: "superadmin",
      role: "superadmin",
    });

    await superAdmin.save();

    res.status(201).json({
      success: true,
      message: "Super admin created successfully",
      user: {
        email: superAdmin.email,
        name: superAdmin.name,
        role: superAdmin.role,
      },
    });
  } catch (error) {
    console.error("Error creating super admin:", error);
    res.status(500).json({
      success: false,
      message: "Error creating super admin",
      error: error.message,
    });
  }
});

// Initialize superadmin user (for production setup)
router.post("/init-superadmin", async (req, res) => {
  try {
    // Check if superadmin already exists
    const existingSuperAdmin = await User.findOne({
      email: "<EMAIL>",
    });

    if (existingSuperAdmin) {
      return res.json({
        success: true,
        message: "Super admin already exists",
        user: {
          email: existingSuperAdmin.email,
          name: existingSuperAdmin.name,
          role: existingSuperAdmin.role,
        },
      });
    }

    // Create superadmin user
    const superAdmin = new User({
      name: "Super Admin",
      email: "<EMAIL>",
      password: "superadmin",
      role: "superadmin",
    });

    await superAdmin.save();

    res.status(201).json({
      success: true,
      message: "Super admin created successfully",
      user: {
        email: superAdmin.email,
        name: superAdmin.name,
        role: superAdmin.role,
      },
    });
  } catch (error) {
    console.error("Error creating super admin:", error);
    res.status(500).json({
      success: false,
      message: "Error creating super admin",
      error: error.message,
    });
  }
});

export default router;
