import jwt from "jsonwebtoken";
import connectDB from "../../backend/config/database.js";
import User from "../../backend/models/User.js";

// Helper function to ensure JSON response
const sendJSONResponse = (res, statusCode, data) => {
  try {
    if (!res.headersSent) {
      res.setHeader("Content-Type", "application/json");
      res.status(statusCode).json(data);
    }
  } catch (error) {
    console.error("Error sending JSON response:", error);
    if (!res.headersSent) {
      res.status(500).end("Internal Server Error");
    }
  }
};

export default async function handler(req, res) {
  try {
    console.log(`[LOGIN] ${new Date().toISOString()} - Request received:`, {
      method: req.method,
      origin: req.headers.origin,
      userAgent: req.headers["user-agent"],
      contentType: req.headers["content-type"],
    });

    // Set CORS headers first
    const origin = req.headers.origin;
    const allowedOrigins = [
      "http://localhost:5173",
      "http://127.0.0.1:5173",
      "https://pluginsight.vercel.app",
      process.env.FRONTEND_URL,
    ].filter(Boolean);

    if (allowedOrigins.includes(origin)) {
      res.setHeader("Access-Control-Allow-Origin", origin);
    }

    res.setHeader("Access-Control-Allow-Credentials", "true");
    res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization"
    );

    // Handle preflight requests
    if (req.method === "OPTIONS") {
      console.log("[LOGIN] Handling OPTIONS preflight request");
      res.status(200).end();
      return;
    }

    // Only allow POST method
    if (req.method !== "POST") {
      console.log(`[LOGIN] Method not allowed: ${req.method}`);
      return sendJSONResponse(res, 405, {
        success: false,
        message: "Method not allowed",
      });
    }

    if (!req.body) {
      console.log("[LOGIN] No request body provided");
      return sendJSONResponse(res, 400, {
        success: false,
        message: "Request body is required",
      });
    }

    console.log("[LOGIN] Attempting database connection...");

    const dbConnectionPromise = connectDB();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Database connection timeout")), 10000)
    );

    await Promise.race([dbConnectionPromise, timeoutPromise]);
    console.log("[LOGIN] Database connected successfully");

    const { email, password } = req.body;
    console.log(
      `[LOGIN] Login attempt for email: ${
        email ? email.substring(0, 3) + "***" : "undefined"
      }`
    );

    if (!email || !password) {
      console.log("[LOGIN] Missing email or password");
      return sendJSONResponse(res, 400, {
        success: false,
        message: "Email and password are required",
      });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.log("[LOGIN] Invalid email format");
      return sendJSONResponse(res, 400, {
        success: false,
        message: "Invalid email format",
      });
    }

    console.log("[LOGIN] Searching for user in database...");

    const userSearchPromise = User.findByEmail(email);
    const userTimeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("User search timeout")), 5000)
    );

    const user = await Promise.race([userSearchPromise, userTimeoutPromise]);

    if (!user) {
      console.log("[LOGIN] User not found");
      return sendJSONResponse(res, 400, {
        success: false,
        message: "Invalid credentials",
      });
    }

    console.log(`[LOGIN] User found: ${user.email}, active: ${user.isActive}`);

    if (!user.isActive) {
      console.log("[LOGIN] User account is deactivated");
      return sendJSONResponse(res, 400, {
        success: false,
        message: "Account is deactivated",
      });
    }

    console.log("[LOGIN] Verifying password...");

    const passwordCheckPromise = user.comparePassword(password);
    const passwordTimeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Password verification timeout")), 5000)
    );

    const isMatch = await Promise.race([
      passwordCheckPromise,
      passwordTimeoutPromise,
    ]);

    if (!isMatch) {
      console.log("[LOGIN] Password verification failed");
      return sendJSONResponse(res, 400, {
        success: false,
        message: "Invalid credentials",
      });
    }

    console.log("[LOGIN] Password verified, updating last login...");

    try {
      user.lastLogin = new Date();
      const savePromise = user.save();
      const saveTimeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("User save timeout")), 5000)
      );

      await Promise.race([savePromise, saveTimeoutPromise]);
      console.log("[LOGIN] Last login updated successfully");
    } catch (saveError) {
      console.error("[LOGIN] Failed to update last login:", saveError);
    }

    console.log("[LOGIN] Generating JWT token...");

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret || jwtSecret === "fallback-secret") {
      console.error("[LOGIN] JWT_SECRET not properly configured");
      return sendJSONResponse(res, 500, {
        success: false,
        message: "Server configuration error",
      });
    }

    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role },
      jwtSecret,
      { expiresIn: "30d" }
    );

    console.log("[LOGIN] Login successful, sending response");

    return sendJSONResponse(res, 200, {
      success: true,
      message: "Login successful",
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
    });
  } catch (error) {
    console.error("[LOGIN] Error during login process:", {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });

    let errorMessage = "Server error";
    let statusCode = 500;

    if (error.message.includes("timeout")) {
      errorMessage = "Request timeout. Please try again.";
      statusCode = 408;
    } else if (error.message.includes("Database")) {
      errorMessage = "Database connection error. Please try again.";
      statusCode = 503;
    } else if (error.name === "ValidationError") {
      errorMessage = "Invalid input data";
      statusCode = 400;
    }

    return sendJSONResponse(res, statusCode, {
      success: false,
      message: errorMessage,
      error:
        process.env.NODE_ENV === "production"
          ? "Internal server error"
          : error.message,
    });
  }
}
